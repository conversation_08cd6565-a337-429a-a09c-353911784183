<?php
// Test script to verify dependency warnings are working correctly
require_once('baffletrain/autocadlt/includes/application_top.php');
require_once('baffletrain/autocadlt/includes/functions/tcs_components.php');
require_once('baffletrain/autocadlt/includes/functions/tcs_attributes_components.php');
require_once('baffletrain/autocadlt/includes/classes/attributes.class.php');

// Test with a specific product ID (replace with an actual product ID that has attributes)
$test_product_id = 1; // Change this to a product ID that exists in your database

echo "<h2>Testing Dependency Warnings</h2>";

echo "<h3>1. Testing get_attributes_tables() function:</h3>";
$result1 = get_attributes_tables($test_product_id);
echo "Result contains dependency warnings: " . (strpos($result1, 'Attribute Dependency Issues') !== false ? 'YES' : 'NO') . "<br>";
echo "Result length: " . strlen($result1) . " characters<br>";

echo "<h3>2. Testing direct tcs_draw_attributes_dependency_warnings() call:</h3>";
$products_attributes = new tcs_product_attributes($test_product_id);
$products_attributes->get_attributes(false); // Initialize attributes
if ($products_attributes->has_variations) {
    $products_attributes->get_variations();
}
$result2 = tcs_draw_attributes_dependency_warnings($products_attributes);
echo "Result contains dependency warnings: " . (strpos($result2, 'Attribute Dependency Issues') !== false ? 'YES' : 'NO') . "<br>";
echo "Result length: " . strlen($result2) . " characters<br>";

echo "<h3>3. Testing check_dependency_issues() method directly:</h3>";
$products_attributes = new tcs_product_attributes($test_product_id);
$issues = $products_attributes->check_dependency_issues();
echo "Number of dependency issues found: " . count($issues) . "<br>";
if (!empty($issues)) {
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li>{$issue['type']}: {$issue['message']}</li>";
    }
    echo "</ul>";
} else {
    echo "No dependency issues found.<br>";
}

echo "<h3>4. Testing attributes data structure:</h3>";
$products_attributes = new tcs_product_attributes($test_product_id);
$attributes = $products_attributes->get_attributes(false);
echo "Number of attribute options: " . count($attributes) . "<br>";
echo "Product has attributes: " . ($products_attributes->has_attributes ? 'YES' : 'NO') . "<br>";
echo "Product has variations: " . ($products_attributes->has_variations ? 'YES' : 'NO') . "<br>";

foreach ($attributes as $option_id => $option) {
    echo "Option {$option_id}: {$option['products_options_name']} - " . count($option['values']) . " values<br>";
    foreach ($option['values'] as $value_id => $value) {
        $depends_on = '';
        if (!empty($value['dependson_options_id']) && !empty($value['dependson_options_values_id'])) {
            $depends_on = " (depends on option {$value['dependson_options_id']}, value {$value['dependson_options_values_id']})";
        }
        echo "&nbsp;&nbsp;- Value {$value_id}: {$value['products_options_values_name']}{$depends_on}<br>";
    }
}

echo "<h3>5. Comparison Test:</h3>";
echo "Both methods should produce the same result:<br>";
echo "Method 1 (get_attributes_tables): " . (empty($result1) ? 'EMPTY' : 'HAS CONTENT') . "<br>";
echo "Method 2 (direct call): " . (empty($result2) ? 'EMPTY' : 'HAS CONTENT') . "<br>";
echo "Results match: " . ($result2 === $result2 ? 'YES' : 'NO') . "<br>";

// Test with a product that definitely has dependency issues
echo "<h3>6. Testing with different product IDs:</h3>";
for ($pid = 1; $pid <= 10; $pid++) {
    $test_attributes = new tcs_product_attributes($pid);
    if ($test_attributes->has_attributes) {
        $test_issues = $test_attributes->check_dependency_issues();
        echo "Product {$pid}: " . count($test_issues) . " dependency issues<br>";
        if (!empty($test_issues)) {
            foreach ($test_issues as $issue) {
                echo "&nbsp;&nbsp;- {$issue['type']}: {$issue['message']}<br>";
            }
        }
    } else {
        echo "Product {$pid}: No attributes<br>";
    }
}

?>
