<?php


/**
 * Generates a bootstrap button with htmx attributes
 *
 * @param string $method The method to use for the request. Defaults to 'post'
 * @param string $endpoint The endpoint to make the request to
 * @param string $target The target element to update with the response
 * @param string $swap The swap mode to use. Defaults to 'outerHTML'
 * @param string $text The text to display on the button
 * @param array $vals An array of values to include in the request
 * @param array $extra_params An array of extra parameters to include in the button element
 *
 * @return string The HTML for the button
 */
function tcs_draw_button_html(string $method = null, string $endpoint = null, string $target = null, string $swap = null, string $text = null, array|null $vals = null, array|null $extra_params = [], string $class = '', $type ='submit')
{
    $vals_attribute = $vals ? " hx-vals='" . json_encode($vals) . "'" : '';
    $method = ($method != null) ?  $method = 'post' : '';
    $method = ($endpoint != null) ?  ' hx-' . $method . '="' . $endpoint . '"' : '';
    $endpoint = ($endpoint != null) ?  ' hx-endpoint="' . $endpoint . '"' : '';
    $target = ($target != null) ?  ' hx-target="' . $target . '"' : '';
    $swap = ($swap != null) ?  ' hx-swap="' . $swap . '"' : '';
    $vals = ($vals != null) ?  ' hx-vals="' . json_encode($vals) . '"' : '';
    print_rr($extra_params);
    $extra_params_out = '';
    foreach ($extra_params as $key => $value) {
        $extra_params_out .= ' ' . $key . '="' . $value . '"';
    }
    return "<button type='{$type}'{$method} hx-indicator='#indicatorLines'{$target}{$swap}{$vals_attribute} class='btn btn-primary btn-sm btn_send' {$class}{$extra_params_out }>{$text}</button>&nbsp;";
}

/**
 * Generates a bootstrap card html element with a title, text and optional class and name.
 * @param string $id The id of the card element.
 * @param string $title The title of the card.
 * @param string $text The text content of the card.
 * @param string $class The class of the card element.
 * @param string $name The name of the card element.
 * @return string The generated html.
 */
function tcs_draw_card_html(string $id, string $title, string $text, string $class = '', string $name = '')
{
    return ' 
        <div class="card" id="' . $id . '">
          <div class="card-header">
            <h3 class="card-title">' . $title . '</h3>
          </div>          
          <div class="card-body">
            <p>' . $text . '</p>
          </div>
          <!-- /.card-body -->
        </div>
        <!-- /.card -->';
}

function tcs_draw_panel_html(string $id, string $title, string $text, string $class = '', string $name = '')
{
    return " 
        <div class='panel {$class}' id='{$id}'>
          <div class='panel-header'>
            <h3 class='panel-title'>{$title}</h3>
          </div>          
          <div class='panel-body'>
            <p>{$text}</p>
          </div>
          <!-- /.panel-body -->
        </div>
        <!-- /.panel -->";
}

function tcs_draw_well_html(string $text, string $class = '', string $id = '', string $title = '', string $name = '')
{
    return " 
        <div class='well {$class}'>
          $text
          <!-- /.well-body -->
        </div>
        <!-- /.well -->";
}
/**
 * Generates an input html element with a set of buttons attached to it.
 * 
 * @param string $type The type of input element to generate.
 * @param string $name The name of the input element.
 * @param string $id The id of the input element.
 * @param string $placeholder The placeholder text for the input element.
 * @param string $label The label text for the input element.
 * @param string $value The value of the input element.
 * @param string $class The class of the input element.
 * @param string $required The required attribute of the input element.
 * @param array $buttons An array of buttons to attach to the input element.
 * 
 * @return string The generated html.
 */
function tcs_draw_input_html(
    string $type,
    string $name,
    string|null $id = '',
    string|null $placeholder = '',
    string|null $label = '',
    string|null $value = '',
    string|null $class = '',
    string|null $required = '',
    array|null $buttons = []
) {
    $buttons_html = '';
    $button_described = '';
    print_rr($buttons, 'buttons');
    if (sizeof($buttons) > 0) {
        foreach ($buttons as $button) {
            $buttons_html .= tcs_draw_button_html(
                $button['method'],
                $button['endpoint'],
                $button['target'],
                $button['swap'],
                $button['text'],
                $button['vals'] ?? null,
                $button['extra_params'] ?? '',
                $button['class'] ?? '',
                $button['type'] ?? 'submit'
            );
            $button_described = 'aria-describedby="' . $button['id'] . '"';
        }
    }
    if ($required == 'true') $required = 'required';
    if (isset($label)) $label_html = '<label class="control-label" for="' . $id . '">' . $label . '</label>';
    else $label_html = '';
    if ($id == '') {
        $id = $name;
    }

    $id = ($id != null) ? ' id="' . $id . '"' : '';
    $class = ($class != null) ? ' class="' . $class . '"' : ' class="form-control variations_form"';
    $placeholder = ($placeholder != null) ? ' placeholder="' . $placeholder . '"' : '';
    $aria_label = ($label != null) ? ' aria-label="' . $label . '"' : '';
    $required = ($required != null) ? ' ' . $required : '';
    return '<div class="mb-3 form-inline">&nbsp;<div class="form-group">' . $label_html . '&nbsp;<input type="' . $type . '" name="' . $name . '"   value = "' . $value . '" ' . $id . $class . $placeholder . $aria_label . $required . '>&nbsp;</div>&nbsp;' . $buttons_html . '</div>';
}

/**
 * Generates HTML for a textarea form control
 *
 * @param string $name
 * @param string $id
 * @param string|null $placeholder
 * @param string|null $label
 * @param string|null $value
 * @param string|null $class
 * @param string|null $required
 * @return string
 */
function tcs_draw_textarea_html(
    string $name,
    string $id,
    string|null $placeholder = '',
    string|null $label = '',
    string|null $value = '',
    string|null $class = '',
    string|null $required = '',
) {
    return '<div class="input-group mb-3">' . '<textarea name="' . $name . '" id="' . $id . '" class="' . ($class ?? 'form-control variations_form') . '" placeholder="' . $placeholder . '" value = "' . $value . '" aria-label="' . $label . '"' . $required . ' rows="10"></textarea></div>';
}

/**
 * Generates an HTML form element with the given parameters.
 * @param string $method The HTTP method to use for the form submission.
 * @param string $endpoint The endpoint to submit the form to.
 * @param string|null $name The name of the form.
 * @param string|null $id The ID of the form.
 * @param string|null $class The class of the form.
 * @param string|null $swap The swap strategy to use for the form submission.
 * @param string|null $target The target element to swap.
 * @param array $vals The values to include in the form submission.
 * @return string The generated HTML form element.
 */
function tcs_draw_form_html(string $method, string $endpoint, string $name = null, string $id = null, string $class = null, string $swap = null, string $target = null, array $vals = [])
{
    $vals_attribute = $vals ? "hx-vals='" . json_encode($vals) . "'" : '';
    $id = ($id != null) ?  ' id="' . $id . '"' : '';
    $name = ($name != null) ?  ' name="' . $name . '"' : '';
    $class = ($class != null) ?  ' class="' . $class . '"' : '';
    $method = ($method != null) ?  $method = 'post' : '';
    $endpoint = ($endpoint != null) ?  ' hx-' . $method . '="' . $endpoint . '"' : '';
    $target = ($target != null) ?  ' hx-target="' . $target . '"' : '';
    $swap = ($swap != null) ?  ' hx-swap="' . $swap . '"' : '';
    return '<form ' . $id . $name . $endpoint . $swap . $vals_attribute . $target . '>';
}
/**
 * Generates an HTML label element for a form control.
 *
 * @param string $text The text of the label.
 * @param string $for The id of the form control that the label is for.
 * @return string The generated HTML label element.
 */
function tcs_draw_label_html(string $text, string $for)
{
    return '<label for="' . $for . '">' . $text . '</label>';
}

/**
 * Generates a <button type="submit"> element.
 *
 * @param string $class The class of the element.
 * @param string $text The text of the element.
 * @return string The generated html.
 */
function tcs_draw_submit_html(string $class, string $text)
{
    return '<button type="submit" class="' . $class . '">' . $text . '</button>';
}

/**
 * Generates a <select> element.
 *
 * @param string $name The name of the element.
 * @param string $id The id of the element.
 * @param array $options An associative array of options, where the keys are the values and the values are the texts.
 * @param string|null $selected The value of the selected option.
 * @param array|null $htmx An associative array of htmx attributes.
 * @return string The generated html.
 */
function tcs_draw_select_html(string $name, string $id = '', array $options = [], string|null $selected = null, array|null $htmx = null, $selected_value = null)
{
    $htmx_out = '';

    if ($htmx != null) {
        $method = ($htmx['hx-method'] != null) ?  $method = $htmx["hx-method"] : "";
        $htmx_out .= ($htmx["hx-endpoint"] != null) ?  " hx-" . $method . "='" . $htmx["hx-endpoint"] . "'" : "";
        $htmx_out .= isset($htmx["hx-target"]) ? " hx-target='" . $htmx["hx-target"] . "'" : "";
        $htmx_out .= isset($htmx["hx-swap"]) ? " hx-swap='" . $htmx["hx-swap"] . "'" : "";
        $htmx_out .= isset($htmx["hx-vals"]) ? " hx-vals='" . json_encode($htmx['hx-vals']) . "'" : "";
        $htmx_out .= isset($htmx["hx-indicator"]) ? " hx-indicator='" . $htmx["hx-indicator"] . "'" : " hx-indicator='#indicatorLines'";
        $htmx_out .= isset($htmx["hx-trigger"]) ? " hx-trigger='" . $htmx["hx-trigger"] . "'" : "";
        $htmx_out .= isset($htmx["hx-include"]) ? " hx-include='" . $htmx["hx-include"] . "'" : "";
    }
    if ($id != '') {
        $id = " id='{$id}'";
    }
    $output = "<select name={$name}{$id} class='form-control variations_form' {$htmx_out}>";
    $output .= '<option value=0>Select</option>';
    if (count($options) > 0) {
        print_rr($options);
        $isList = array_is_list($options);
        foreach ($options as $value => $option) {
            $value = !$isList ? $value : $option; 
            $text = $option;
            $selected_attr = ($text == $selected) ? 'selected' : '';
            $selected_attr = ($value == $selected_value) ? 'selected' : '';
            $output .= '<option value="' . $value . '" ' . $selected_attr . '>' . $text . '</option>';
        }
    }
    $output .= '</select>';
    return $output;
}


/**
 * Output a table for administrative purposes with a title, header, body, and footer
 *
 * @param string $title Table title
 * @param string $id Table ID
 * @param string $class Table classes
 * @param array $columns Table column definitions. Each column is an associative array with keys 'name' and 'class'.
 * @param array $dataset Table row data. Each row is an associative array with keys 'id', 'class', and 'content'.
 * @param string $footer Table footer content
 * @return string The rendered table
 */
function tcs_draw_admin_table($title, $id = "", $class = "", $columns, $rows, $footer = "", $params = []){
    $table_attributes = '';
    if (!empty($params) && is_array($params)) {
        foreach ($params as $key => $value) {
            $table_attributes .= " {$key}='{$value}'";
        }
    }
    $table_container = "
    <div class='panel panel-default'>
        <div class='panel-header'>
        <div class='panel-body'>{$title}</div></div>";

    $table_header = "<table id='{$id}' class='{$class}' {$table_attributes}><thead><tr>";
    foreach ($columns as $column) {
        extract($column, EXTR_PREFIX_ALL, "col"); // Extract columns into $col_name, $col_class
        if (!empty($col_params) && is_array($col_params)) {
            foreach ($params as $key => $value) {
                $attributes .= " data-{$key}='{$value}'";
            }
        }
        $table_header .= "<td class='{$col_class}' id='{$col_id}' {$col_params}>{$col_name}</td>" . PHP_EOL;
    }
    $table_header .=  "</tr></thead>";
    $table_body = "<tbody id='{$id}_body'>";
    $table_rows = $rows;
    $table_body .= $table_rows;
    $table_body .= "</tbody>";
    $table_footer = "</table><div class='panel-footer'>{$footer}</div></div>";
    ////print_rr($table_container . $table_header . $table_body . $table_footer);
    return $table_container . $table_header . $table_body . $table_footer;
}

/**
 * Outputs a table row for use in an admin table.
 *
 * @param string $row_id The id of the row
 * @param string $row_class The class of the row
 * @param array $row_content The content of the row. Each cell is an associative array with keys 'class', 'id', 'text', 'data', and 'content'.
 * @return string The rendered row
 */
function tcs_draw_admin_table_row($row_id = "", $row_class = "", $row_content = [], $params = [])
{
    ////print_rr($row_content, 'row_content');
    $params_string = '';
    foreach ($params as $key => $param) {
        $params_string .= " {$key}='{$param}' ";
    }
    $row = "<tr id='{$row_id}' class='{$row_class}' {$params_string}>";
    foreach ($row_content as $cell) {
        ////print_rr($cell, 'rcell_content');
        $row .= "<td";
        foreach ($cell as $key => $value) {
            if ($key == 'text') continue;
            $row .= " $key='$value'";
        }
        $row .= ">{$cell['text']}</td>";
        ////print_rr($row);
    }
    $row .= '</tr>';
    return $row;
}

// specific components

function tcs_draw_product_options_select($products_attributes, $products_id)
{
    $products_options_name_query = tep_db_query("SELECT DISTINCT popt.products_options_id, popt.products_options_name FROM products_options popt, products_attributes patrib WHERE patrib.products_id='{$products_id}' AND patrib.options_id = popt.products_options_id ORDER BY popt.products_options_name");

    if (tep_db_num_rows($products_options_name_query)) {
        while ($products_options_name = tep_db_fetch_array($products_options_name_query)) {
            $select_output = '<div class="form-group attribute_group form-inline">
                            <label for="input_' . $products_options_name['products_options_id'] . '" class="control-label"> ' . $products_options_name['products_options_name'] . ': </label>
                            <select name="id[' . $products_options_name['products_options_id'] . ']" class="form-control variations_form variationsAttributeSelect">';

            $products_options_query = tep_db_query("SELECT pov.products_options_values_id, pov.products_options_values_name FROM products_options_values pov, products_attributes pa WHERE pa.products_id='{$products_id}' AND pa.options_id='{$products_options_name['products_options_id']}' AND pa.options_values_id=pov.products_options_values_id");

            $select_output_options = '<option disabled selected value>None</option>';
            while ($products_options = tep_db_fetch_array($products_options_query)) {
                $select_output_options .= '<option value="' . $products_options['products_options_values_id'] . '">' . $products_options['products_options_values_name'] . '</option>';
            }
            $select_output .= $select_output_options . '</select></div>';
            return $select_output;
        }
    }
}

function tcs_draw_variations_form($id, $products_id, $values = [])
{
    $products_options_name_query = tep_db_query("select distinct popt.products_options_id, popt.products_options_name from products_options popt, products_attributes patrib where patrib.products_id='" . $products_id . "' and patrib.options_id = popt.products_options_id order by popt.products_options_name");
    $select_output_selects = [
        "<common>" => [
            "class" => "form-control variations_form"
        ]
    ];
    if (tep_db_num_rows($products_options_name_query)) {
        while ($products_options_name = tep_db_fetch_array($products_options_name_query)) {
            $products_options_query = tep_db_query("select pov.products_options_values_id, pov.products_options_values_name, pa.options_values_price, pa.price_prefix, pa.attribute_default, pa.dependson_options_id, pa.dependson_options_values_id from products_attributes pa, products_options_values pov where pa.products_id = '" . $products_id . "' and pa.options_id = '" . (int)$products_options_name['products_options_id'] . "' and pa.options_values_id = pov.products_options_values_id order by pa.products_attributes_sort_order");
            $select_id = "attribute[{$products_options_name['products_options_id']}]";
            $select_output_selects[$select_id] = [
                "node_type" => "select",
                "label" => $products_options_name['products_options_name'],
                "options" => []
            ];
            while ($products_options = tep_db_fetch_array($products_options_query)) {
                $select_output_selects[$select_id]['options'][$products_options['products_options_values_id']] = $products_options['products_options_values_name'];
            }
        }
    }



    $array = [
        "form" => [
            "<cfg>" => [
                "common" => [
                    "name" => "name_<node_type>",
                    "id" => "<name>_<node_type>",
                    "label" => "<name>",
                    "value" => "<values_list>"
                ],
                "noTag" => true,
                "id" => "variations_form",
                "class" => "variations_form"
            ],
            "inputs" => [
                "node_type" => "group",
                "class" => "col-sm-3 well",
                "elements" => [
                    "<common>" => [
                        "node_type" => "input",
                        "class" => "form-control variations_form",
                    ],
                    "model" => [],
                    "GTIN" => [],
                    "image_id" => [],
                    "price" => [],
                    "products_id" => [
                        "type" => "hidden",
                        "value" => $products_id
                    ]
                ]
            ],
            "right_elements" => [
                "node_type" => "group",
                "class" => "col-sm-9",
                "elements" => [
                    "attributes" => [
                        "node_type" => "group",
                        "class" => "col-sm-6 well",
                        "elements" => $select_output_selects
                    ],
                    "autodesk_linking" => [
                        "node_type" => "group",
                        "class" => "col-sm-6 well",
                        "elements" => [
                            "<common>" => [
                                "class" => "form-control variations_form"
                            ],
                            "terms" => [
                                "node_type" => "input",
                                "label" => ""
                            ],
                            "search" => [
                                "node_type" => "button",
                                "hx-trigger" => "click",
                                "hx-get" => "api_h.php?action=product_autodesk_link_search",
                                "hx-target" => "#autodesk_link_select",
                                "hx-include" => "#terms_input",
                                "hx-swap" => "innerHTML"
                            ],
                            "autodesk_link" => [
                                "node_type" => "select"
                            ]
                        ]
                    ]
                ]
            ],
            "controls" => [
                "node_type" => "group",
                "class" => "col-sm-9 well text-right",
                "elements" => [
                    "submit" => [
                        "node_type" => "button",
                        "hx-trigger" => "click",
                        "hx-get" => "api_h.php?action=product_save",
                        "hx-indicator" => "#indicatorLines",
                        "hx-include" => ".variations_form",
                        "hx-target" => "#variations_table_body",
                        "hx-swap" => "beforeend"
                    ],
                    "cancel" => [
                        "node_type" => "button",
                        "hx-trigger" => "click",
                        "hx-get" => "api_h.php?action=product_cancel",
                        "hx-indicator" => "#indicatorLines",
                        "hx-include" => ".variations_form",
                        "hx-target" => "#variations_form",
                        "hx-swap" => "outerHTML"
                    ]
                ]
            ]
        ]
    ];
    ////print_rr($array,"renderarray");
    return renderForm($array, $values);
}


function renderForm($config, $values = [])
{
    $formContent = render_node($config['form'], $config['form']['<cfg>']['common'] ?? [], $values);
    if ($config['form']['<cfg>']['noTag']) return "<div class='\"'row {$config['form']['<cfg>']['class']}' id='{$config['form']['<cfg>']['id']}'>"  .  $formContent . "</DIV>";
    return "<form class='{$config['form']['<cfg>']['class']}' id='{$config['form']['<cfg>']['id']}'>
                {$formContent}
            </form>";
}

function render_node($nodes, $common = [], $values = [])
{

    $formContent = '';
    if (is_array($nodes['<common>'])) $common = array_merge($common, $nodes['<common>']);
    foreach ($nodes as $node_name => $node) {
        if ($node_name == "<common>" || $node_name == "<cfg>") continue;
        switch ($node['node_type'] ?? "element") {
            case 'group':
                $formContent .= renderGroup($node, $common, $values);
                break;
            default:
                ////print_rr($node,"node_name: " . $node_name); 
                ////print_rr($node,"node_common: " . $node_name); 
                if (is_array($node)) $node['name'] = $node_name;
                $formContent .= renderElement($node, $common, $values);
        }
    }
    return $formContent;
}

function renderGroup($group, $common = [], $values = [])
{
    $common = array_merge($common, $group['elements']['<common>'] ?? []);
    $groupHtml = "<div class='" . $group['class'] . " '>";
    $groupHtml .= render_node($group['elements'], $common, $values);
    $groupHtml .= "</div>";
    return $groupHtml;
}

function renderElement($elementConfig, $common = [], $values = [])
{

    $elementConfig = array_merge($common, $elementConfig);
    foreach ($elementConfig as $key => $value) {
        $value = str_replace('<name>',  $elementConfig['name'], $value);
        $value = str_replace('<id>',    $elementConfig['id'], $value);
        $value = str_replace('<label>', $elementConfig['label'], $value);
        $value = str_replace('<node_type>',  $elementConfig['node_type'], $value);

        switch ($key) {
            case 'label':
                $value = mb_convert_case(str_replace('_', ' ', $value), MB_CASE_TITLE, "UTF-8");
                break;
            case 'name':
                $value = strtolower($value);
                break;
            case 'value':
                print_rr($values, 'values feg:' . $elementConfig['name']);
                if ($value == "<values_list>") {
                    $value = '';
                    $theName = $elementConfig['name'];
                    if (isset($values[$theName])) $value = $values[$theName];
                    //print_rr($values[$theName],'$values[$theName]: ' . '$values[' . $theName . ']' . $values[$theName]);
                };
                $elementConfig["values_name"] =  $values[$elementConfig['name'] . '_value_name'];
                break;
        }
        $elementConfig[$key] = $value;
    }
    //print_rr($values,"values");
    switch ($elementConfig['node_type']) {
        case 'input':
            return renderInput($elementConfig);
        case 'select':
            return renderSelect($elementConfig);
        case 'button':
            return renderButton($elementConfig);
        default:
            return '';
    }
}




function renderInput($config)
{
    ////print_rr($config,'config flarp');
    $label = $config['label'] ?? '';

    $type = $config['type'] ?? 'text';

    unset($config['label']);
    unset($config['node_type']);
    $inputHtml = '';
    if ($type != 'hidden' && $label != '') $inputHtml = "<label for=\"{$config['id']}\">{$label}</label>";
    $input_attribs = '';
    foreach ($config as $key => $value) {
        $input_attribs .= " $key='$value' ";
    }
    $inputHtml .= "<input{$input_attribs}>";

    return $inputHtml;
}

function renderSelect($config)
{
    $name = $config['name'] ?? '';
    $class = $config['class'] ?? "";
    $id = $config['id'] ?? "";
    $label = $config['label'] ?? "";

    $selectHtml = $label ? "<label for='$id'>$label</label>" : '';
    $selectHtml .= "<select name='$name' id='$id' class='$class'>";
    print_rr($config, 'select config');
    if (!is_null($config['value']) && $config['value'] != '') {
        if (!isset($config['options'])) {
            $selectHtml .= "<option value='{$config['value']}' selected>{$config['values_name']}</option>";
        }
    }

    if (isset($config['options'])) {
        foreach ($config['options'] as $value => $option) {
            $selected = '';
            if (isset($config['value']) && $config['value'] == $value) $selected = 'selected';
            $selectHtml .= "<option value='{$value}'{$selected}>{$option}</option>";
        }
    }
    $selectHtml .= "</select>";
    return $selectHtml;
}


function renderButton($config)
{
    $name = $config['name'] ?? '';
    $class = $config['class'] ?? '';
    $id = $config['id'] ?? '';
    $label = $config['label'] ?? 'Submit';

    $attributes = [
        'type' => 'button',
        'id' => $id,
        'class' => $class,
        'hx-trigger' => $config['hx-trigger'] ?? null,
        'hx-get' => $config['hx-get'] ?? null,
        'hx-post' => $config['hx-post'] ?? null,
        'hx-target' => $config['hx-target'] ?? null,
        'hx-include' => $config['hx-include'] ?? null,
        'hx-params' => $config['hx-params'],
        'hx-swap' => $config['hx-swap'] ?? null
    ];

    $buttonHtml = "<button ";
    foreach ($attributes as $attr => $value) {
        if ($value !== null) {
            $buttonHtml .= "{$attr}='{$value}' ";
        }
    }
    $buttonHtml .= ">$label</button>";

    return $buttonHtml;
}



function tcs_draw_variations_table_row($variation, $params = [])
{
    print_rr($variation, 'variations');
    $attributes = explode('{', substr($variation['attribute_string'], strpos($variation['attribute_string'], '{') + 1));
    $attributesNameString = "";
    for ($i = 0, $n = sizeof($attributes); $i < $n; $i++) {
        $pair = explode('}', $attributes[$i]);
        $attributesNameString .= '<span style="font-weight:bold">' . tep_options_name($pair[0]) . ':</span> ';
        $attributesNameString .= tep_values_name($pair[1]) . ' ';
    }

    $rowId = $variation['products_variations_id'];
    $rowClass = ($variation['enabled'] == "0") ? "table-danger danger" : "variation_row";
    $autodesk_link = "";
    $autodesk_link = $variation['autodesk_link_name'];
    if ($variation['autodesk_link'] != "") {
        $autodesk_link = $autodesk_link;
    }
    // Add this row's data to the dataset
    $id = "variations_table_row_{$rowId}";

    $row_content = [
        ['class' => 'portletTD',          'id' => '',                                     'text' => '<div class="portlet">&nbsp;</div>'],
        ['class' => '',                   'id' => "variations_table_model_{$rowId}",      'text' => $variation['model']],
        ['class' => '',                   'id' => "variations_table_gtin_{$rowId}",       'text' => $variation['gtin']],
        ['class' => 'text-center',        'id' => "variations_table_image_id_{$rowId}",   'text' => $variation['image_id']],
        ['class' => 'text-center',        'id' => "variations_table_Price_{$rowId}",      'text' => $variation['price']],
        ['class' => '',                   'id' => "variations_table_attributes_{$rowId}", 'text' => $attributesNameString],
        ['class' => '',                   'id' => '',                                     'text' => '<input class="indexSO variationsSOinput" type="text" name="" size="1" maxlength="4" value="' . $variation['sort_order'] . '">'],
        ['class' => '',                   'id' => '',                                     'text' => $autodesk_link],
        [
            'class' => 'listsOptionsEdit',
            'id' => '',
            'text' => 'e',
            'hx-target' => '#variations_form',
            'hx-get' => 'api_h.php?action=product_variations_product_edit',
            'hx-vals' => '{"products_id":"' . $variation['products_id'] . '", "products_variations_id":"' . $variation['products_variations_id'] . '"}'
        ],
        [
            'class' => 'listsOptionsDelete',
            'id' => '',
            'text' => 'x',
            'hx-target' => "#{$id}",
            'hx-get' => 'api_h.php?action=product_variations_removeVariation',
            'hx-vals' => '{"products_variations_id":"' . $variation['products_variations_id'] . '"}'
        ],
    ];
    print_rr($params, 'paramamaamama');
    $params['data-variationsid'] = $rowId;
    // Output the table
    return tcs_draw_admin_table_row($id, $rowClass, $row_content, $params);
}



function tcs_draw_variations_table($products_attributes, $products_id)
{
    // Define columns for the table
    $columns = [
        ['name' => '&nbsp;', 'class' => '', 'params' => ''],
        ['name' => 'Model', 'class' => '', 'params' => ''],
        ['name' => 'GTIN', 'class' => '', 'params' => ''],
        ['name' => 'Image ID', 'class' => 'text-center', 'params' => ''],
        ['name' => 'Price', 'class' => 'text-center', 'params' => ''],
        ['name' => 'Options', 'class' => 'text-center', 'params' => ''],
        ['name' => 'Sort Order', 'class' => 'text-center', 'params' => ''],
        ['name' => 'Autodesk Link', 'class' => 'text-center', 'params' => ''],
        ['name' => 'Actions', 'class' => 'text-center', 'params' => ['colspan' => '2']],
    ];

    // Get the variations
    $variations = $products_attributes->get_variations();
    //print_rr($products_attributes);
    $rows = '';
    foreach ($variations as $key_a => $variation) {
        $rows .= tcs_draw_variations_table_row($variation);
    }
    $footer = "<div class='panel-footer varitionsFooter'>
        <div id='variations_options_container' class=''>
            <div class='row'>";
    $footer .= tcs_draw_variations_form($products_attributes, $products_id);
    $footer .= "</div></div></div>";
    $datashix = [
        'Variations Table',
        'variations_table',
        'table table-striped table-draggable',
        $columns,
        $rows,
        $footer
    ];
    //print_rr($datashix, 'datashix');
    // Output the table
    return tcs_draw_admin_table(
        'Variations Table',
        'variations_table',
        'table table-striped table-draggable',
        $columns,
        $rows,
        $footer
    );
}

function tcs_draw_attributes_form($products_id,$selected_attributes = null, $products_attributes = null){
    global $currencies;
    $options_output = "<div id='attributes_form'>";
    if (!is_object($products_attributes)) {
        $products_attributes = new tcs_product_attributes($products_id);
    }
    $attributes = $products_attributes->attributes;

    //print_rr($products_attributes);
    $fr_input = $fr_required = $fr_feedback = null;

        $fr_input    = FORM_REQUIRED_INPUT;
        $fr_required = 'required aria-required="true" '; 
        $fr_feedback = ' has-feedback';

    if (MODULE_CONTENT_PI_OA_HELPER == 'True') {
        $products_options_array[] = array('id' => '', 'text' => MODULE_CONTENT_PI_OA_ENFORCE_SELECTION);            
    }		
    foreach ($attributes as $aKey => $products_options) {
        $products_options_array = array();
        $select_output_select = '<div class="form-group attribute_group ' . $fr_feedback . '">
            <label for="input_' . $aKey . '" class="control-label col-sm-3">' . $products_options['products_options_name'] . '</label>
                <div class="col-sm-9 attributeWrapper">
                    <select name="id[' . $aKey . ']" data-optionid="' . $aKey . '" data-productid="' . (int)$products_id . '" data-optionsid="' . $aKey . '" data-lastVal="" required="" aria-required="true" id="input_' . $aKey . '" class="form-control attributeSelect" style="display:none">
        ';
        $buttons_output = '<div id="attributes_values_id_btns_container" class="attributes_values_id_btns_container btn-group" data-optionid="' . $aKey . '" >';
        $option_selected = false;
        $select_output_options = "";
        foreach ($products_options['values'] as $vKey => $products_options_values) {
            $selected_option = false;
            if (is_array($selected_attributes) && isset($selected_attributes[$aKey][$vKey])) {
                    $selected_option = true;                
            } elseif (isset($products_attributes->selected_attributes[$aKey][$vKey])) {
                $selected_option = true;
            }
            if ($selected_option) {
                $selected_option = 'selected';
                $selected_button = 'active btn-success';
            } else {
                $selected_option = '';
                $selected_button = '';
            }
            $optionsPrice = $currencies->display_price($products_options_values['options_values_price'], tep_get_tax_rate(1));
            $select_output_options .= '<option value="' . $vKey . '" data-productId="' . tep_get_prid($products_id) . '" data-priceIncrease="' . $products_options_values['options_values_price'] . '" ' . $selected_option . ' data-dependson_optionsid="' . $products_options_values['dependson_options_id'] . '" data-dependsOn_valuesid="' . $products_options_values['dependson_options_values_id'] . '">' . $products_options_values['products_options_values_name'];
            if ($products_options_values['options_values_price'] != '0') {
                $select_output_options .= ' (' . $products_options_values['price_prefix'] . $optionsPrice . ') ';
            }
            $select_output_options .= '</option>';           
            $vals = "{
                \"action\": \"update_attributes_form\",
                \"optionid\": \"{$aKey}\",
                \"valueid\": \"{$vKey}\",
                \"productId\": \"{$products_id}\",
                \"priceIncrease\": \"{$products_options_values['options_values_price']}\",
                \"data-dependson_optionsid\": \"{$products_options_values['dependson_options_id']}\",
                \"dependsOn_valuesid\": \"{$products_options_values['dependson_options_values_id']}\"
            }";
            $buttons_output .= "<button type='button' hx-post='api_h.php' hx-target='#attributes_form' hx-swap='outerHTML' hx-vals='{$vals}' id='valuesSelectBtns_{$aKey}_{$vKey}'   class='btn btn-default valuesSelectBtns {$selected_button}' >{$products_options_values['products_options_values_name']} </button>";
        }

        if (!$option_selected) {
            $select_output = $select_output_select . '<option disabled selected value>None</option>' . $select_output_options;
        }
        $select_output .=   "</select>";
        $buttons_output .= "</div>
            </div>
        </div>";
        $buttons_output .= "";

        $options_output .= $select_output . $buttons_output;
    }
    $options_output = "</div>";
    return $options_output;
}
