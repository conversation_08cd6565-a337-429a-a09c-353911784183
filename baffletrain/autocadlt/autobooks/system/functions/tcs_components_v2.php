<?php



/**
 * Generates a tailwind ui card html element with a title, text and optional class and name.
 * @param string $id The id of the card element.
 * @param string $title The title of the card.
 * @param string $text The text content of the card.
 * @param string $class The class of the card element.
 * @param string $name The name of the card element.
 * @return string The generated html.
 */
function tcs_icon($icon, $class = '') {
     return tcs_get_icon($icon, $class);
 }
 function tcs_get_icon($icon, $class = '') {
    $class = $class ? " class='{$class}'" : '';
     return "<svg $class " . ICON[$icon] . "></svg>";
 }

function tcs_draw_card_html(string $content, string $id = "", string $class = "", string|null $header = "", string|null $footer = "", ) {

    if ($header) {
        $header = "<!-- header --><div id='{$id}_card_header' class='px-0 py-0 sm:px-0'>{$header}</div><!-- header end -->";
    }
    if ($footer){
        $footer = "<!-- footer --><div id='{$id}_card_footer' class='px-0 py-0 sm:px-0 {$class}'>{$footer}</div><!-- footer end -->";
    }
   
    $content = "<!-- card content --><div id='{$id}_card_body' class='px-0 py-0 sm:p-0'>{$content}</div><!-- card content end -->";
    return "<!-- card --><div id='{$id}_card' class='divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow {$class}'>{$header}{$content}{$footer}</div><!-- card end -->";
}

function tcs_draw_card_bootstrap_html(string $content, string|null $class = "", string|null $header = "", string|null $footer = "", ) {
    
    if ($header) $header = "<div class='panel-header'>{$header}</div>";
    if ($footer) $footer = "<div class='panel-footer {$class}'>{$footer}</div>";
    
    $content = $content;
    return "<div class='col-sm-10 col-xs-12'> <div class='panel panel-default {$class}'> {$header}{$content}{$footer}</div></div>";
}

function tcs_draw_panel_html(string $id, string $title, string $text, string $class = '', string $name = '') {
    return " 
        <div class='panel {$class}' id='{$id}'>
          <div class='panel-header'>
            <h3 class='panel-title'>{$title}</h3>
          </div>          
          <div class='panel-body'>
            <p>{$text}</p>
          </div>
          <!-- /.panel-body -->
        </div>
        <!-- /.panel -->";
}

function tcs_draw_well_html(string $text, string $class = '', string $id = '', string $title = '', string $name = '') {
    return " 
        <div class='well {$class}'>
          $text
          <!-- /.well-body -->
        </div>
        <!-- /.well -->";
}

/**
 * Generates an HTML form element with the given parameters.
 * @param string $method The HTTP method to use for the form submission.
 * @param string $endpoint The endpoint to submit the form to.
 * @param string|null $name The name of the form.
 * @param string|null $id The ID of the form.
 * @param string|null $class The class of the form.
 * @param string|null $swap The swap strategy to use for the form submission.
 * @param string|null $target The target element to swap.
 * @param array $vals The values to include in the form submission.
 * @return string The generated HTML form element.
 */
function tcs_draw_form (array $cfg, string|null $lib = null){
    $cfg['type'] = 'form';
    return tcs_draw_form_ctrl($cfg,$lib);

}

function tcs_draw_form_element(array $cfg, string|null $lib = null){
    return tcs_draw_form_ctrl($cfg,$lib);
}


function tcs_draw_button(array $cfg, string|null $lib = null){
    if (isset($cfg['type'])) $cfg['sub_type'] = $cfg['type'];
    $cfg['type'] = 'button';
    return tcs_draw_form_ctrl($cfg,$lib);   
}

function tcs_draw_input(array $cfg, string|null $lib = null){
    if (isset($cfg['type'])) $cfg['sub_type'] = $cfg['type'];
    $cfg['type'] = 'input';
    return tcs_draw_form_ctrl($cfg,$lib);   
}

function tcs_draw_input_button_group(array $cfg, string|null $lib = null){


//     <div>
//     <label for="query" class="block text-sm/6 font-medium text-gray-900">Search candidates</label>
//     <div class="mt-2 flex">
//       <div class="-mr-px grid grow grid-cols-1 focus-within:relative">
//         <input type="text" name="query" id="query" class="col-start-1 row-start-1 block w-full rounded-l-md bg-white py-1.5 pl-10 pr-3 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:pl-9 sm:text-sm/6" placeholder="John Smith">
//         <svg class="pointer-events-none col-start-1 row-start-1 ml-3 size-5 self-center text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
//           <path d="M8.5 4.5a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0ZM10.9 12.006c.11.542-.348.994-.9.994H2c-.553 0-1.01-.452-.902-.994a5.002 5.002 0 0 1 9.803 0ZM14.002 12h-1.59a2.556 2.556 0 0 0-.04-.29 6.476 6.476 0 0 0-1.167-2.603 3.002 3.002 0 0 1 3.633 1.911c.18.522-.283.982-.836.982ZM12 8a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z" />
//         </svg>
//       </div>
//       <button type="button" class="flex shrink-0 items-center gap-x-1.5 rounded-r-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 hover:bg-gray-50 focus:relative focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600">
//         <svg class="-ml-0.5 size-4 text-gray-400" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
//           <path fill-rule="evenodd" d="M2 2.75A.75.75 0 0 1 2.75 2h9.5a.75.75 0 0 1 0 1.5h-9.5A.75.75 0 0 1 2 2.75ZM2 6.25a.75.75 0 0 1 .75-.75h5.5a.75.75 0 0 1 0 1.5h-5.5A.75.75 0 0 1 2 6.25Zm0 3.5A.75.75 0 0 1 2.75 9h3.5a.75.75 0 0 1 0 1.5h-3.5A.75.75 0 0 1 2 9.75ZM9.22 9.53a.75.75 0 0 1 0-1.06l2.25-2.25a.75.75 0 0 1 1.06 0l2.25 2.25a.75.75 0 0 1-1.06 1.06l-.97-.97v5.69a.75.75 0 0 1-1.5 0V8.56l-.97.97a.75.75 0 0 1-1.06 0Z" clip-rule="evenodd" />
//         </svg>
//         Sort
//       </button>
//     </div>
//   </div>
   
    $cfg['class_override'] = true;
    $cfg['container'] = false;
    $cfg_input = $cfg['input'] ?? null;    unset($cfg['input']);
    $cfg_button = $cfg['button'] ?? null;    unset($cfg['button']);
    $cfg_input['type'] = 'input';
    $cfg_input['sub_type'] = 'text';
    $cfg_input['icon_class'] = 'class="pointer-events-none col-start-1 row-start-1 ml-3 size-5 self-center text-gray-400 sm:size-4"';
    $cfg_input['class'] .= 'col-start-1 row-start-1 block w-full rounded-l-md bg-white py-1.5 pl-10 pr-3 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:pl-9 sm:text-sm/6';
    $cfg_button['type'] = 'button';
    $cfg_button['icon_class'] = '-ml-0.5 size-4 text-gray-400';
    $cfg_button['class'] .= 'flex shrink-0 items-center gap-x-1.5 rounded-r-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 hover:bg-gray-50 focus:relative focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600';
    
    $cfg_input['label'] = "";
    $group = "
    <div>
        <label for='{$cfg_input['name']}' class='block text-sm/6 font-medium text-gray-900'>{$cfg['label']}</label>
        <div class='mt-2 flex'>
            <div class='-mr-px grid grow grid-cols-1 focus-within:relative'>
                " . tcs_draw_form_ctrl(array_merge($cfg,$cfg_input),$lib) . "
            </div> 
            " . tcs_draw_form_ctrl(array_merge($cfg,$cfg_button),$lib) . "
        </div>
    </div>";
    return $group;
}

function tcs_draw_select(array $cfg, string|null $lib = null){
    if (isset($cfg['type'])) $cfg['sub_type'] = $cfg['type'];
    $cfg['type'] = 'select';
    return tcs_draw_form_ctrl($cfg,$lib);   
}

function tcs_draw_textarea(array $cfg, string|null $lib = null){
    if (isset($cfg['type'])) $cfg['sub_type'] = $cfg['type'];
    $cfg['type'] = 'textarea';
    return tcs_draw_form_ctrl($cfg,$lib);   
}



function tcs_draw_form_ctrl(array $cfg, string|null $lib = null) {
    // Define output structure
    $out = [
        'class' => '',
        'class_override' => false,
        'label' => '',
        'type' => $cfg['type'] ?? '',
        'content' => '',
        'sub_type' => $cfg['sub_type'] ?? 'text',
        'name' => '',
        'id' => '',
        'attributes' => $cfg['attributes'] ?? '',
        'icon' => '',
        'container' => $cfg['container'] ?? true,
        'alpine_dispatch' => '',
        'extra_params' => '',
        'value' => ''
    ];
//    print_rr($cfg);
    
    // Set framework-specific classes
    if ($lib === "bootstrap") {
        $container_class = "form-group";
        $label_class = "control-label";
        $classes = [
            "text" => "form-control",
            "input" => "form-control",
            "checkbox" => "form-control",
            "radio" => "form-control",
            "button" => "form-control",
            "select" => "form-control"
        ];
    } else {
        $container_class = "flex items-center relative";
        $label_class = "block text-sm font-medium leading-6 text-gray-900";
        $classes_list = [
            'default' => [
                "form" => [
                    "elm" => "",
                    "label" => "block text-sm font-medium leading-6 text-gray-900"
                ],
                "text" => [
                    "elm" => "w-full rounded-md border-0 p-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                    "label" => "block text-sm font-medium leading-6 text-gray-900"
                ],
                "input" => [
                    "elm" => "w-full rounded-md border-0 p-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                    "label" => "block text-sm font-medium leading-6 text-gray-900"
                ],
                "checkbox" => [
                    "elm" => "h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600",
                    "label" => "block text-sm font-medium leading-6 text-gray-900"
                ],
                "radio" => [
                    "elm" => "h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600",
                    "label" => "block text-sm font-medium leading-6 text-gray-900"
                ],
                "button" => [
                    "elm" => "pointer-events-auto rounded-md px-4 py-2 text-center font-medium shadow-sm ring-1 ring-slate-700/10 hover:bg-slate-50",
                    "label" => "block text-sm font-medium leading-6 text-gray-900"
                ],
                "select" => [
                    "elm" => "mt-2 block w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6",
                    "label" => "block text-sm font-medium leading-6 text-gray-900"
                ],
                "textarea" => [
                    "elm" => "block w-full rounded-md border-0 p-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                    "label" => "block text-sm font-medium leading-6 text-gray-900"
                ]
            ],
            'compact' => [
                "text" => [
                    "elm" => "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm/6",
                    "label" => "absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900",
                ]
            ],
            'no_border' => [
                "button" => [
                    "elm" => "-m-2.5 p-2.5 text-gray-400 hover:text-gray-500",
                    "label" => "",
                ]
            ]
         ];
    }
    $out['end_tag'] = "</{$out['type']}>";
    $classes = $classes_list[$cfg['variant'] ?? 'default'];
    // Handle specific type-based configurations
    switch ($cfg['type']) {
        case 'form':
            $out['type'] = 'form';
            break;
        case 'button':
            $out['content'] = $cfg['label'];
            $cfg['label'] = "";
            if (isset($cfg['sub_type'])) {
                $out['type'] = "button";
                $out['sub_type'] = " type='{$cfg['sub_type']}'";
            }else{
                $out['type'] = isset($cfg['href']) ? 'a' : 'button';
            }            
            break;

        case 'select': 
            if (isset($cfg['options'])) $cfg['content'] = $cfg['options'];
            if (isset($cfg['content']) && is_array($cfg['content'])) {
                $out['content'] = "";
              
                foreach ($cfg['content'] as $key => $value) {
                    $selected = $key == ($selected ?? '') ? ' selected' : '';
                    $out['content'] .= "<option value='{$key}'{$selected}>{$value}</option>";
                }
            }
            break;
        case 'input':
            $out['end_tag'] = "";
           if (empty($cfg['value']) && isset($cfg['content']) ) $cfg['value'] = $cfg['content'];
            $out['value'] = " value='{$cfg['value']}'";
            if ($cfg['sub_type'] === "hidden") {
                $cfg['container_class'] = "hidden";
                $out['class'] = " class='hidden'";
            } else {
                $out['type'] = 'input';
                $out['extra_params'] .= " type='{$out['type']}'";
            }  
           
            break;
        case 'textarea':
                $out['type'] = $cfg['type'];
                $out['content'] .= "{$cfg['content']}";
                break;
        default:
            $out['type'] = $cfg['type'];        
    }
//    print_rr($out['value'], 'out value');
    if ($cfg['type'] !== 'form') {
         // Set element class and additional class if provided
        if (!$cfg['class_override']) {            
            $out['class'] = $classes[$out['type']]['elm'] ?? $classes[$out['sub_type']]['elm'];
            $out['class'] .= isset($cfg['class']) ? " {$cfg['class']}" : '';
        }else{
            $out['class'] = isset($cfg['class']) ? " {$cfg['class']}" : '';
        }
        $out['class'] = " class='{$out['class']}'";

        if (isset($cfg['name'])) {
            $out['name'] = isset($cfg['name']) ? " name='{$cfg['name']}'" : '';
            $out['id'] = isset($cfg['id']) ? " id='{$cfg['id']}'" : " id='{$cfg['name']}_{$out['type']}'";
        } else {
            $out['name'] = '';
            $out['id'] = isset($cfg['id']) ? " id='{$cfg['id']}'" : "";
        }
        
        // Set label if provided
        if (isset($cfg['label']) && $cfg['label'] !== '') {
            $label_class = $classes[$out['type']]['label'] ?? $classes[$out['sub_type']]['label'];
            $out['label'] = "<label class='{$label_class}' for='{$cfg['id']}'>{$cfg['label']}</label>";
        }
       

       
    }
    if(isset($cfg['icon'])) {
        $out['icon'] = tcs_get_icon($cfg['icon'], $cfg['icon_class']);
    }
   
    // Add extra parameters that aren't in $out by default
    $extra_params = array_diff_key($cfg, $out);
    foreach ($extra_params as $key => $value) {
        if (is_array($value)){
            $out['extra_params'] .= " {$key}='" . json_encode($value) . "'";
        }else{
            if (is_int($key)) { 
                $out['extra_params'] .= " $value";
            }else{
                $out['extra_params'] .= " {$key}='{$value}'";
            }
        }        
    }
   
    $output = "{$out['label']}
                <{$out['type']}{$out['id']}{$out['class']}{$out['name']}{$out['extra_params']}{$out['value']}>{$out['icon']}{$out['content']}{$out['end_tag']}";

    if ($out['container']){
        $output = "<div class='{$container_class}'>{$output}</div>";
    }
    // Return constructed HTML
    return $output;
}




/**
 * Output a table for administrative purposes with a title, header, body, and footer
 *
 * @param string $title Table title
 * @param string $id Table ID
 * @param string $class Table classes
 * @param array $columns Table column definitions. Each column is an associative array with keys 'name' and 'class'.
 * @param array $dataset Table row data. Each row is an associative array with keys 'id', 'class', and 'content'.
 * @param string $footer Table footer content
 * @return string The rendered table
 */
function tcs_draw_admin_table($title, $id = "", $class = "col", $columns, $rows, $footer = "") {

    $svg_col_sort = "" . 
    "<span class='invisible flex-none ml-2 text-gray-400 rounded group-focus:visible group-hover:visible'>
        <svg class='w-5 h-5' viewBox='0 0 20 20' fill='currentColor' aria-hidden='true'>
        <path fill-rule='evenodd' d='M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z' clip-rule='evenodd' />
        </svg>
    </span>";
    $table_header = "<!-- table -->
    <table id='{$id}_table' class='min-w-full divide-y divide-gray-300 htmx-swapping:opacity-0 transition-opacity duration-1000 {$class}'>
         <thead scope='col' class='bg-gray-50'>
            <tr>";
     
    $default_col_class = "px-3 py-3.5 text-left text-sm font-semibold text-gray-900 whitespace-nowrap";
    $default_column = ["name" => "Name", "class" => "", "id" => "", "params" => []];
    foreach ($columns as $column) {
        $column = array_merge($default_column, $column);
        $col_class_override = false;
        extract($column, EXTR_PREFIX_ALL, "col"); // Extract columns into $col_name, $col_class
        $attributes = "";
        if (!empty($col_params) && is_array($col_params)) {
            foreach ($col_params as $key => $value) {
                $attributes .= " {$key}='{$value}'";
            }
        }
        if (!$col_class_override) $col_class = "{$default_col_class} {$col_class}";
        $table_header .= "<th class='$col_class' id='{$col_id}' {$attributes}>
        <a href='#' class='inline-flex group'> {$col_name} $svg_col_sort</a></th>";
    }
    $table_header .=  "</tr></thead>";
    $table_body = "<tbody  <tbody class='bg-white divide-y divide-gray-200' id='{$id}_body'>";
    $table_rows = $rows;
    $table_body .= $table_rows;
    $table_body .= "</tbody>";
    $table_footer = "</table><!-- /table -->";
    $table = $table_header . $table_body . $table_footer;
    return tcs_draw_card_html($table, $title, $footer);
}
function tcs_draw_admin_bootstrap_table($title, $columns, $rows, $id = "", $class = "", $footer = "",$table_params = [], $body_only = false) {
    foreach ($table_params as $key => $value) {
        $attributes .= " data-{$key}='{$value}'";
    }

    $table_body = "<tbody id='{$id}_body'>";
    $table_rows = $rows;
    $table_body .= $table_rows;
    $table_body .= "</tbody>";

    // If body_only is true, return just the tbody content
    if ($body_only) {
        return $table_body;
    }

    $table_header = "<table class='table-draggable table-striped table' id='variationsTable' ><thead scope='col'><tr>";

    $default_col_class = "";
    foreach ($columns as $column) {
        $col_class_override = false;
        extract($column, EXTR_PREFIX_ALL, "col"); // Extract columns into $col_name, $col_class
        if (!empty($col_params) && is_array($col_params)) {
            foreach ($params as $key => $value) {
                $attributes .= " data-{$key}='{$value}'";
            }
        }
        if (!$col_class_override) $col_class = $default_col_class . $col_class;
        $table_header .= "<th class='$col_class' id='{$col_id}' {$col_params}>
        <a href='#' class='inline-flex group'> {$col_name} $svg_col_sort</a></th>";
    }
    $table_header .=  "</tr></thead>";
    $table_footer = "</table>";
    $table = $table_header . $table_body . $table_footer;
    return tcs_draw_card_bootstrap_html($table,null, $title, $footer);
}
/**
 * Outputs a table row for use in an admin table.
 *
 * @param string $row_id The id of the row
 * @param string $row_class The class of the row
 * @param array $row_content The content of the row. Each cell is an associative array with keys 'class', 'id', 'text', 'data', and 'content'.
 * @return string The rendered row
 */
function tcs_draw_admin_bootstrap_table_row($row_id = "", $row_class = "", $row_content = [], $params = []) {

    $params_string = '';
    foreach ($params as $key => $param) {
        $params_string .= " {$key}='{$param}' ";
    }
    $row = "<tr id='{$row_id}' class='{$row_class}' {$params_string}>";


    $info_class = "";
    $title_class = "";
    $action_class = "attributesOptionsDelete listsOptionsDelete";

    foreach ($row_content as $cell) {
        $cell_attributes = "";
        if (!empty($cell['params']) && is_array($cell['params'])) {
            foreach ($params as $key => $value) {
                $cell_attributes .= " {$key}='{$value}'";
            }
        }
        $cell_content = $cell['content'];
        $cell_class = $info_class;
        switch ($cell['type']) {
            case 'info':
                $cell_class = $info_class;
                break;
            case 'title':
                $cell_class = $title_class;
                break;
            case 'action':
                $cell_class = $action_class;
                break;
        }


        if (isset($cell['class_override']) && $cell['class_override']) {
            $cell_class = $cell['class'];
        } else {
            $cell_class .= " {$cell['class']}";
        }
        $row .= "<td class='{$cell_class}' id='{$cell['id']}' {$cell_attributes}>{$cell_content}</td>";
    }
    $row .= '</tr>';
    return $row;
}

function generate_nav_tree($items, $root_element, $root_params, $node_element, $node_class, $parent = "", $path = "") {
    $out = "<{$root_element} class='{$root_class}'>";

    if ($path) $path .= '/';
    if ($parent) $path .= $parent;
    foreach ($items as $key => $item) {
        $out .= "<{$node_element} {$root_params}>";
        $out .= generate_nav_link($key,$item,isset($item['subfolder']),$path);
        if (isset($item['sub_folder']) ) {
            $out .= "<div x-data='{ open: true }'>";
            $out .= generate_nav_tree($item['sub_folder'],  $root_element, $root_params, $node_element, $node_class, $item['name'], $path);
            $out .= "</div>";
        }
    }
    $out .= "</{$root_element}>";
    return $out;
}

function generate_nav_link($key, $item, $folder = false, $path) {
    //print_rr($item);
    $icons = ICONS;
    $folder_out = "";
    if ($folder) $folder_out = "aria-controls='sub-menu-1' @click='open = !open' aria-expanded='false' x-bind:aria-expanded='open.toString()'";
    $view = $key;
    if ($path) $path .= '/';
    $app_root = APP_ROOT;
    $path = strtolower(str_replace(' ', '_', "{$path}{$view}"));
    $url_path = ltrim($path, '/');
   // print_rr(['path' => $path, 'url_path' => $url_path]);
    $out = "<a href='{$app_root}{$url_path}' hx-get='{$app_root}getview/{$path}' hx-target='#content_wrapper' hx-replace-url='{$app_root}{$url_path}' {$folder_out} class='flex gap-x-3 p-2 text-sm font-semibold leading-6 text-indigo-200 rounded-md group hover:text-white hover:bg-indigo-700' x-state:on='Current' x-state:off='Default' x-state-description='Current: &quot;bg-indigo-700 text-white&quot;, Default: &quot;text-indigo-200 hover:text-white hover:bg-indigo-700&quot;'>";
    $out .= "<span class='shrink-0'>{$icons[$item['icon']]}</span> <span class='transition-opacity duration-300 whitespace-nowrap' :class='collapsed ? \"opacity-0\" : \"opacity-100\"'>{$item['name']}</span>";
    $out .= "</a>";
    return $out;
}
// Function to generate a text input
function tcs_autogen_datadisplay_textinput($label, $name, $value) {

    //  <label for="name" class="-top-2 absolute left-2 inline-block px-1 text-xs font-medium text-gray-900 bg-white">Name</label>
    //  <input type="text" name="name" id="name" class="w-full block py-1.5 text-gray-900 rounded-md border-0 ring-1 ring-inset ring-gray-300 shadow-sm focus:ring-2 focus:ring-inset focus:ring-indigo-600 placeholder:text-gray-400 sm:text-sm sm:leading-6" placeholder="Jane Smith">
  
      return "
      <div class='relative'>
          <label class='-top-2 absolute left-2 inline-block px-1 text-xs font-medium text-gray-900 bg-white' for='{$name}'>{$label}</label>
            <input type='text' id='{$name}' name='{$name}' value='{$value}' readonly
                 class='w-full block py-1.5 pl-1.5 text-gray-900 rounded-md border-0 ring-1 ring-inset ring-gray-300 shadow-sm focus:ring-2 focus:ring-inset focus:ring-indigo-600 placeholder:text-gray-400 sm:text-sm sm:leading-6'>
      </div>";
  }
  
  // Function to generate a card with collapsible sections for arrays
  function tcs_autogen_datadisplay_card($title, $content, $open = false) {
      return "
      <div x-data='{ open: $open }' class='col-span-4 bg-white rounded-lg shadow-md'>
          <button @click='open = !open' class='w-full px-4 py-2 text-lg font-semibold text-left text-gray-700 focus:outline-none hover:bg-gray-100'>
              {$title}
              <span x-show='!open' class='ml-2'>+</span>
              <span x-show='open' class='ml-2'>-</span>
          </button>
          <div x-show='open' class='grid grid-cols-4 gap-4 px-4 py-2 border-t border-gray-200'>
              {$content}
          </div>
      </div>";
  }
  
  // Function to generate a form section for associative arrays
  function tcs_autogen_datadisplay_section($data, $sectionTitle, $open = false) {
      $content = '';
      $hasContent = false;
      //print_rr($data, "generate section");
      foreach ($data as $key => $value) {
          if (is_array($value)) {
              $temp_content = tcs_autogen_datadisplay_card(ucwords(str_replace('_', ' ', $key)), tcs_autogen_datadisplay_section($value, $key));
              if (!$temp_content) continue;
              $content .= $temp_content;
              $hasContent = true;
          } else {
              if (empty($value)) continue;            
              $content .= tcs_autogen_datadisplay_textinput(ucwords(str_replace('_', ' ', $key)), $key, $value);            
              $hasContent = true;
          }
      }
      if ($hasContent) return $content; else return false;
  }
  


function tcs_draw_admin_table_row($row_id = "", $row_class = "", $row_content = [], $params = [], $wrap_tr = true) {
    $params_string = '';
    if (!empty($params) && is_array($params)) {
        foreach ($params as $key => $param) {
            $params_string .= " {$key}='{$param}' ";
        }
    }
    $row = "";
    if ($wrap_tr) $row = "<tr id='{$row_id}' class='{$row_class}' {$params_string}>";

    $fst_title_class =      "py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6'";
    $title_class =          "px-3 py-3.5 text-left text-sm font-semibold text-gray-900";
    $fst_info_class =       "whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6";
    $info_class =           "whitespace-nowrap px-3 py-4 text-sm text-gray-500";    
    $action_class =         "relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6";

    $count = 0;
   // print_rr($row_content);
    foreach ($row_content as $cell) {

        $cell_type = $cell['type'] ?? "info";
        $cell_id = $cell['id'] ?? "";
        if ($count == 0)  $cell_type = "fst_{$cell_type}";   
        $count++;

        $cell_attributes = "";
        if (!empty($cell['params']) && is_array($cell['params'])) {
            foreach ($params as $key => $value) {
                $cell_attributes .= " {$key}='{$value}'";
            }
        }
        $cell_content = $cell['content'];
        
        if (isset($cell['class_override']) && $cell['class_override']) {
            $cell_class = $cell['class'];
        } else {
            $cell_class = match($cell_type) {
                'info' => $info_class,
                'title' => $title_class,
                'action' => $action_class,
                'fst_info' => $fst_info_class,
                'fst_title' => $fst_title_class,
                'fst_action' => $action_class,
                'fst_' => $fst_info_class,
                default => $info_class
            };
            $cell_class .= " {$cell['class']}";
        }
        $row .= "<td class='{$cell_class}' id='{$cell_id}' {$cell_attributes}>{$cell_content}</td>";
    }
    if ($wrap_tr) $row .= '</tr>';
    return $row;
}
// specific components

function tcs_draw_product_options_select($products_attributes, $products_id) {
    $products_options_name_query = tep_db_query("SELECT DISTINCT popt.products_options_id, popt.products_options_name FROM products_options popt, products_attributes patrib WHERE patrib.products_id='{$products_id}' AND patrib.options_id = popt.products_options_id ORDER BY popt.products_options_name");

    if (tep_db_num_rows($products_options_name_query)) {
        while ($products_options_name = tep_db_fetch_array($products_options_name_query)) {
            $select_output = '<div class="attribute_group form-group form-inline">
                            <label for="input_' . $products_options_name['products_options_id'] . '" class="control-label"> ' . $products_options_name['products_options_name'] . ': </label>
                            <select name="id[' . $products_options_name['products_options_id'] . ']" class="variationsAttributeSelect variations_form">';

            $products_options_query = tep_db_query("SELECT pov.products_options_values_id, pov.products_options_values_name FROM products_options_values pov, products_attributes pa WHERE pa.products_id='{$products_id}' AND pa.options_id='{$products_options_name['products_options_id']}' AND pa.options_values_id=pov.products_options_values_id");

            $select_output_options = '<option disabled selected value>None</option>';
            while ($products_options = tep_db_fetch_array($products_options_query)) {
                $select_output_options .= '<option value="' . $products_options['products_options_values_id'] . '">' . $products_options['products_options_values_name'] . '</option>';
            }
            $select_output .= $select_output_options . '</select></div>';
            return $select_output;
        }
    }
}

function tcs_draw_variations_form($id, $products_id, $values = []) {
    $products_options_name_query = tep_db_query("select distinct popt.products_options_id, popt.products_options_name from products_options popt, products_attributes patrib where patrib.products_id='" . $products_id . "' and patrib.options_id = popt.products_options_id order by popt.products_options_name");
    $select_output_selects = [
        "<common>" => [
            "class" => "variations_form"
        ]
    ];
    if (tep_db_num_rows($products_options_name_query)) {
        while ($products_options_name = tep_db_fetch_array($products_options_name_query)) {
            $products_options_query = tep_db_query("select pov.products_options_values_id, pov.products_options_values_name, pa.options_values_price, pa.price_prefix, pa.attribute_default, pa.dependson_options_id, pa.dependson_options_values_id from products_attributes pa, products_options_values pov where pa.products_id = '" . $products_id . "' and pa.options_id = '" . (int)$products_options_name['products_options_id'] . "' and pa.options_values_id = pov.products_options_values_id order by pa.products_attributes_sort_order");
            $select_id = "attribute[{$products_options_name['products_options_id']}]";
            $select_output_selects[$select_id] = [
                "type" => "select",
                "label" => $products_options_name['products_options_name'],
                "content" => []
            ];
            while ($products_options = tep_db_fetch_array($products_options_query)) {
                $select_output_selects[$select_id]['content'][$products_options['products_options_values_id']] = $products_options['products_options_values_name'];
            }
        }
    }



    $array = [
        "form" => [
            "type"=>"group",            
            "<common>" => [
                "name" => "name_<type>",
                "id" => "<name>_<type>",
                "label" => "<name>",
                "value" => "<values_list>",
                "lib" => "bootstrap"
            ],
            "<common_group>" => [
                    "class" => "col-md-5",
            ],
            "<common_label>" => [
                "class" => "control-label"
            ],         
            "noTag" => true,
            "id" => "variations_form",
            "class" => "variations_form",
            "inputs" => [
                "type" => "group",             
                "elements" => [
                    "<common>" => [
                        "type" => "input"
                    ],
                    "model" => [],
                    "GTIN" => [],
                    "image_id" => [],
                    "price" => [],
                    "products_id" => [
                        "sub_type" => "hidden",
                        "value" => $products_id
                    ]
                ]
            ],
            "right_elements" => [
                "type" => "group",
                "elements" => [
                    "attributes" => [
                        "type" => "group",
                        "class" => "col-md-6",
                        "elements" => $select_output_selects
                    ],
                    "autodesk_linking" => [
                        "type" => "group",
                        "class" => "col-md-6",
                        "elements" => [
                            "<common>" => [
                            ],
                            "terms" => [
                                "type" => "input",
                                "label" => ""
                            ],
                            "search" => [
                                "type" => "button",
                                "hx-trigger" => "click",
                                "hx-get" => "api/product_autodesk_link_search",
                                "hx-target" => "#autodesk_link_select",
                                "hx-include" => "#terms_input",
                                "hx-swap" => "innerHTML"
                            ],
                            "autodesk_link" => [
                                "type" => "select"
                            ]
                        ]
                    ]
                ]
            ],
            "controls" => [
                "type" => "group",
                "class" => "col-md-1 form-inline space-x-2 space-y-2 align-items-right",
                "elements" => [
                    "submit" => [
                        "type" => "button",
                        "hx-trigger" => "click",
                        "hx-get" => "api/product_save",
                        "hx-indicator" => "#indicatorLines",
                        "hx-include" => ".variations_form",
                        "hx-target" => "#variations_table_body",
                        "hx-swap" => "beforeend"
                    ],
                    "cancel" => [
                        "type" => "button",
                        "hx-trigger" => "click",
                        "hx-get" => "api/product_cancel",
                        "hx-indicator" => "#indicatorLines",
                        "hx-include" => ".variations_form",
                        "hx-target" => "#variations_form",
                        "hx-swap" => "outerHTML"
                    ]
                ]
            ]
        ]
    ];
    //print_rr($array,"renderarray");
    return renderForm($array, $values);
}



function renderForm($config, $values = []) {
    $formContent = render_node($config['form'], $config['form']['common'], $values);
    if ($config['form']['noTag']) return "<div class='\"row {$config['form']['class']}' id='{$config['form']['id']}'>"  .  $formContent . "</DIV>";
    return "<form class='{$config['form']['class']}' id='{$config['form']['id']}'>
                {$formContent}
            </form>";
}

function render_node($nodes, $common = [], $values = []) {
    $formContent = '';

    // Initialize $common array to include 'all' and type-specific keys
    if (is_array($nodes['<common>'])) {
        $common['all'] = array_merge($common['all'] ?? [], $nodes['<common>']);
    } 
      
    foreach ($nodes as $node_name => $node) {
        //print_rr(is_array($node)  . " != 0 OR " . strpos($node_name, "<common") . " >= 0 ", "node_name: " . $node_name);
         if (!is_array($node) || strpos($node_name, "<common") !== false ) continue;
           $type = $node['type'];
        
        // Add type-specific common if present
        if (isset($nodes["<common_{$type}>"])) {
            $common[$type] = array_merge($nodes["<common_{$type}>"], $common[$type] ?? []);
        }

        // Select common configuration based on type
       

        switch ($type) {
            case 'group':
                $formContent .= renderGroup($node, $common, $values);
                break;
            default:
                if (is_array($node)) $node['name'] = $node_name;
                $formContent .= renderElement($node, $common, $values);
        }
    }
    return $formContent;
}

function renderGroup($group, $common = [], $values = []) {
    
    $group = array_merge($common['all'] ?? [], $group);
    if ($common['group']) $group = array_merge($common['group'], $group);
    $groupHtml = render_node($group['elements'], $common, $values);
    if ($group['lib'] == 'bootstrap') return "<div class='form-group {$group['class']}'>$groupHtml</div>";
    return tcs_draw_card_html($groupHtml, $group['class']);
}

function renderElement($elementConfig, $common = [], $values = []) {
    $nodeCommon = array_merge($common['all'] ?? [], $common[$elementConfig['type']] ?? []);


    $elementConfig = array_merge($nodeCommon, $elementConfig);
    foreach ($elementConfig as $key => $value) {
        $value = str_replace('<name>', $elementConfig['name'], $value);
        $value = str_replace('<id>', $elementConfig['id'], $value);
        $value = str_replace('<label>', $elementConfig['label'], $value);
        $value = str_replace('<type>', $elementConfig['type'], $value);

        switch ($key) {
            case 'label':
                $value = mb_convert_case(str_replace('_', ' ', $value), MB_CASE_TITLE, "UTF-8");
                break;
            case 'name':
                $value = strtolower($value);
                break;
            case 'value':
                if ($value == "<values_list>") {
                    $value = '';
                    $theName = $elementConfig['name'];
                    if (isset($values[$theName])) $value = $values[$theName];
                }
                $elementConfig["values_name"] = $values[$elementConfig['name'] . '_value_name'] ?? '';
                break;
        }
        $elementConfig[$key] = $value;
    }

    return tcs_draw_form_element($elementConfig);
}









function tcs_draw_variations_table($products_attributes, $products_id) {
    // Define columns for the table
    $columns = [
        ['name' => '&nbsp;', 'class' => '', 'params' => ''],
        ['name' => 'Model', 'class' => '', 'params' => ''],
        ['name' => 'GTIN', 'class' => '', 'params' => ''],
        ['name' => 'Image ID', 'class' => 'text-center', 'params' => ''],
        ['name' => 'Price', 'class' => 'text-center', 'params' => ''],
        ['name' => 'Options', 'class' => 'text-center', 'params' => ''],
        ['name' => 'Sort Order', 'class' => 'text-center', 'params' => ''],
        ['name' => 'Autodesk Link', 'class' => 'text-center', 'params' => ''],
        ['name' => 'Actions', 'class' => 'text-center', 'params' => ['colspan' => '2']],
    ];

    // Get the variations
    $variations = $products_attributes->get_variations();
    //print_rr($variations,'prodad');
    $rows = '';
    foreach ($variations as $key_a => $variation) {
        $rows .= tcs_draw_variations_table_row($variation);
    }
    $footer = "<div class='varitionsFooter'>
        <div id='variations_options_container' class=''>
            <div class='row'>";
    $footer .= tcs_draw_variations_form($products_attributes, $products_id);
    $footer .= "</div></div></div>";
    
    /*print_rr( "
        'Variations Table',
        'variations_table',
        'table table-striped table-draggable',
        $columns,
        $rows,
        $footer
    ", 'datashix');*/
    // Output the table
    return tcs_draw_admin_bootstrap_table(
        'Variations Table',
        'variations_table',
        'table table-striped table-draggable',
        $columns,
        $rows,
        $footer
    );
}
function tcs_draw_variations_table_row($variation, $params = []) {
    //print_rr($variation, 'variations');
    $attributes = explode('{', substr($variation['attribute_string'], strpos($variation['attribute_string'], '{') + 1));
    $attributesNameString = "";
    for ($i = 0, $n = sizeof($attributes); $i < $n; $i++) {
        $pair = explode('}', $attributes[$i]);
        $attributesNameString .= '<span style="font-weight:bold">' . tep_options_name($pair[0]) . ':</span> ';
        $attributesNameString .= tep_values_name($pair[1]) . ' ';
    }

    $rowId = $variation['products_variations_id'];
    $rowClass = ($variation['enabled'] == "0") ? "table-danger danger" : "variation_row";
    $autodesk_link = "";
    $autodesk_link = $variation['autodesk_link_name'];
    if ($variation['autodesk_link'] != "") {
        $autodesk_link = $autodesk_link;
    }
    // Add this row's data to the dataset
    $id = "variations_table_row_{$rowId}";

    $row_content = [
        ['class' => 'portletTD',          'id' => '',                                     'content' => '<div class="portlet">&nbsp;</div>'],
        ['class' => '',                   'id' => "variations_table_model_{$rowId}",      'content' => $variation['model']],
        ['class' => '',                   'id' => "variations_table_gtin_{$rowId}",       'content' => $variation['gtin']],
        ['class' => 'text-center',        'id' => "variations_table_image_id_{$rowId}",   'content' => $variation['image_id']],
        ['class' => 'text-center',        'id' => "variations_table_Price_{$rowId}",      'content' => $variation['price']],
        ['class' => '',                   'id' => "variations_table_attributes_{$rowId}", 'content' => $attributesNameString],
        ['class' => '',                   'id' => '',                                     'content' => '<input class="indexSO variationsSOinput" type="text" name="" size="1" maxlength="4" value="' . $variation['sort_order'] . '">'],
        ['class' => '',                   'id' => '',                                     'content' => $autodesk_link],
        [
            'class' => 'listsOptionsEdit',
            'id' => '',
            'text' => 'e',
            'hx-target' => '#variations_form',
            'hx-get' => 'api/product_variations_product_edit',
            'hx-vals' => '{"products_id":"' . $variation['products_id'] . '", "products_variations_id":"' . $variation['products_variations_id'] . '"}'
        ],
        [
            'class' => 'listsOptionsDelete',
            'id' => '',
            'text' => 'x',
            'hx-target' => "#{$id}",
            'hx-get' => 'api/product_variations_removeVariation',
            'hx-vals' => '{"products_variations_id":"' . $variation['products_variations_id'] . '"}'
        ],
    ];
    //print_rr($params, 'paramamaamama');
    $params['data-variationsid'] = $rowId;
    // Output the table
    return tcs_draw_admin_bootstrap_table_row($id, $rowClass, $row_content, $params);
}



function tcs_draw_attributes_form($products_id, $products_attributes = null, $selected_attributes = null,) {
    global $currencies;
    // //print_rr($products_attributes, 'products_attributes');
    $options_output = "<div id='attributes_form'>";
    if (!is_object($products_attributes)) {
        $products_attributes = new tcs_product_attributes($products_id);
    }
    $attributes = $products_attributes->get_attributes();

    //print_rr($products_attributes);
    $fr_input = $fr_required = $fr_feedback = null;
    $fr_input    = FORM_REQUIRED_INPUT;
    $fr_required = 'required aria-required="true" ';
    $fr_feedback = ' has-feedback';

    foreach ($attributes as $aKey => $products_options) {
        $select_output_select = '<div class="attribute_group form-group' . $fr_feedback . '"><label for="input_' . $aKey . '" class="col-sm-3 control-label">' . $products_options['products_options_name'] . '</label><div class="attributeWrapper col-sm-9"><select name="attributes[' . $aKey . ']" required="" aria-required="true" id="input_' . $aKey . '" class="attributes_selects form-control" style="display:none">';
        $buttons_output = '<div id="attributes_values_id_btns_container" class="attributes_values_id_btns_container btn-group" data-optionid="' . $aKey . '" >';
        $option_selected = false;
        $select_output_options = "";
        foreach ($products_options['values'] as $vKey => $products_options_values) {
            $selected_option = false;
            if (is_array($selected_attributes) && isset($selected_attributes[$aKey][$vKey])) {
                $selected_option = true;
            } elseif (isset($products_attributes->selected_attributes[$aKey][$vKey])) {
                $selected_option = true;
            }
            if ($selected_option) {
                $selected_option = 'selected';
                $selected_button = 'active btn-success';
            } else {
                $selected_option = '';
                $selected_button = '';
            }
            $optionsPrice = $currencies->display_price($products_options_values['options_values_price'], tep_get_tax_rate(1));
            $select_output_options .= '<option value="' . $vKey . '" data-productId="' . tep_get_prid($products_id) . '" data-priceIncrease="' . $products_options_values['options_values_price'] . '" ' . $selected_option . ' data-dependson_optionsid="' . $products_options_values['dependson_options_id'] . '" data-dependsOn_valuesid="' . $products_options_values['dependson_options_values_id'] . '">' . $products_options_values['products_options_values_name'];
            if ($products_options_values['options_values_price'] != '0') {
                $select_output_options .= ' (' . $products_options_values['price_prefix'] . $optionsPrice . ') ';
            }
            $select_output_options .= '</option>';
            $vals = "{\"action\": \"update_attributes_form\",\"options_id\": \"{$aKey}\",\"values_id\": \"{$vKey}\",\"products_id\": \"{$products_id}\"}";
            $buttons_output .= "<button type='button' hx-post='api_h.php' hx-target='#attributes_form' hx-swap='outerHTML' hx-vals='{$vals}' hx-include='.attributes_selects' id='valuesSelectBtns_{$aKey}_{$vKey}'   class='btn btn-default valuesSelectBtns {$selected_button}' >{$products_options_values['products_options_values_name']} </button>";
        }
        if (!$option_selected) {
            $select_output = $select_output_select . '<option disabled selected value>None</option>' . $select_output_options;
        }
        $select_output .=   "</select>";
        $buttons_output .= "</div></div></div>";
        $buttons_output .= "";
        $options_output .= $select_output . $buttons_output;
    }
    $options_output .= "</div>";

    return $options_output;
}

function tcs_draw_cart_dropdown($cart, $currencies) {
    include("resourcess/languages/english/modules/navbar_modules/nb_shopping_cart.php");
    $cart_link_url = tep_href_link('shopping_cart.php');
    $cart_link_a = "<a href='{$cart_link_url}'>";
    $cart_link_text = sprintf(MODULE_NAVBAR_SHOPPING_CART_HAS_CONTENTS, $cart->count_contents(), $currencies->format($cart->show_total()));
    $cart_link_html = "{$cart_link_a}{$cart_link_text}</a>";
    $dropdown = [
        "id" => "shopping_cart_dropdown",
        "class" => "shopping_cart",
        "toggle_text" => sprintf(MODULE_NAVBAR_SHOPPING_CART_CONTENTS, $cart->count_contents()),
        "entries" => [
            "$cart_link_html",
            "<divider>"
        ]
    ];
    $products = $cart->get_products();
    foreach ($products as $k => $v) {
        $products_attributes = new tcs_product_attributes($v['id'], 1);
        $attribute_string = "{" . explode("{", $v['id'], 2)[1];
        $dropdown['entries'][] = sprintf("<a href='{$cart_link_url}'>%s x %s</a>", $v['quantity'], $v['name'] . $products_attributes->generate_product_suffix($attribute_string));
    }
    $dropdown['entries'][] = "<divider>";
    $dropdown['entries'][] = "{$cart_link_a}" . MODULE_NAVBAR_SHOPPING_CART_VIEW_CART . "</a>";
    return tcs_draw_dropdown($dropdown, "li");
}

function tcs_draw_dropdown($config, $element = "div") {
    $output = "<{$element} class='dropdown' id='{$config['id']}'>";
    $output .= "<a class='dropdown-toggle' id='{$config['id']}_toggle' data-toggle='dropdown'  data-toggle='popover' title='' href='#'>{$config['toggle_text']}</a>
               <ul class='dropdown-menu' id='{$config['id']}_menu'>";
    foreach ($config['entries'] as $key => $entry) {
        if ($entry == "<divider>") {
            $output .= "<li role='separator' class='divider'></li>";
            continue;
        }
        $output .= "<li>{$entry}</li>";
    }
    $output .= "</ul></{$element}>";

    return $output;
}

function tcs_draw_products_price_widget($product_info, $products_attributes, $currencies, $content_width = "6") {

    $products_price = $currencies->display_price($product_info['products_price'], tep_get_tax_rate($product_info['products_tax_class_id']));
    $specials_price = null;

    $products_price_raw = $product_info['products_price'];
    if ($products_attributes->has_variations) {
        $products_price_raw = $products_attributes->get_current_selected_variation()['price'];
    } else if ($products_attributes->has_attributes) {
        $attributes = $products_attributes->get_current_selected_attributes();
        if ($attributes['price_prefix'] == '+') {
            $products_price_raw += $attributes['attributes_values_price'];
        } else if ($attributes['price_prefix'] == '*') {
            $products_price_raw = $attributes['attributes_values_price'] > 0 ? $products_price_raw * $attributes['attributes_values_price'] : $products_price_raw;
        } else if ($attributes['price_prefix'] == '-') {
            $products_price_raw -= $attributes['attributes_values_price'];
        }
    }

    if ($thePrice = tep_get_products_special_price($product_info['products_id'])) {
        $VAT = $currencies->display_price($thePrice + tep_calculate_tax($thePrice, tep_get_tax_rate($product_info['products_tax_class_id'])), tep_get_tax_rate($product_info['products_tax_class_id'])) . ' Inc. VAT';
    } else {
        $VAT = $currencies->display_price($products_price_raw + tep_calculate_tax($products_price_raw, tep_get_tax_rate($product_info['products_tax_class_id'])), tep_get_tax_rate($product_info['products_tax_class_id'])) . ' Inc. VAT';
    }
    if ($new_price = tep_get_products_special_price($product_info['products_id'])) {
        $specials_price = $currencies->display_price($new_price, tep_get_tax_rate($product_info['products_tax_class_id']));
    }
    if (@tep_not_null($specials_price)) {
        $VAT = '<br> <span id=productsPriceIncTax>(ex VAT ' . $currencies->display_price($thePrice + tep_calculate_tax($thePrice, tep_get_tax_rate($product_info['products_tax_class_id'])), tep_get_tax_rate($product_info['products_tax_class_id'])) . ' Inc. VAT' . ')</span>';
        $products_price = '<del>' . $currencies->display_price($products_price_raw, tep_get_tax_rate($product_info['products_tax_class_id'])) . '</del> <span class="productSpecialPrice" itemprop="price" id="productInfoPrice" content="' . $currencies->display_raw($new_price, tep_get_tax_rate($product_info['products_tax_class_id'])) . '">' . $currencies->display_price($new_price, tep_get_tax_rate($product_info['products_tax_class_id'])) . '</span>';
    } else {
        $products_price = '<span id="productInfoPrice">' . $currencies->display_price($products_price_raw, tep_get_tax_rate($product_info['products_tax_class_id'])) . '</span>';
        $VAT = '<br> <span id="productsPriceIncTax">(ex VAT ' . $currencies->display_price($products_price_raw + tep_calculate_tax($products_price_raw, tep_get_tax_rate($product_info['products_tax_class_id'])), tep_get_tax_rate($product_info['products_tax_class_id'])) . ' Inc. VAT' . ')</span>';
    }
    $output = "<div class='col-sm-{$content_width} cm-pi-price' hx-swap-oob='true' id='cm-pi-price'><div class='productsPrice text-right-not-xs'><div class='productsPrice text-right-not-xs'>{$products_price}{$VAT}</div></div></div>";
    return $output;
}

function tcs_draw_products_info_widget($product_info, $products_attributes, $content_width = "6") {
    if (@tep_not_null($product_info['products_model'])) {
        $products_model = $product_info['products_model'];
        $products_gtin = $product_info['products_gtin'];
    }

    if ($products_attributes->has_variations) {
        $variations = $products_attributes->get_current_selected_variation();
        //print_rr($variations);
        //echo 'mpn: ' . $variations['mpn'];
        if (@tep_not_null($variations['model'])) $products_model = $variations['model'];
        if (@tep_not_null($variations['gtin'])) $products_gtin = $variations['gtin'];
    }
    $products_model_out = !empty($products_model) ? "<dt>Product Code</dt><dd id='products_model'>{$products_model}</dd>" : "";
    $products_gtin_out = !empty($products_gtin) ? "<dt>GTIN</dt><dd id='products_gtin'>{$products_gtin}</dd>" : "";
    $products_manufacturer_out = !empty($products_manufacturer) ? "<dt>Manufacturer</dt><dd id='products_manufacturer'>{$products_manufacturer}</dd>" : "";
    return "<div  hx-swap-oob='true' id='cm-pi-model' class='col-sm-{$content_width} cm-pi-model'><dl class='dl-horizontal list-group-item-text small'>{$products_manufacturer_out}{$products_model_out}{$products_gtin_out}</dl></div>";
}


function tcs_draw_products_name_widget($product_info, $products_attributes, $content_width = "12") {
    if (strpos($product_info['products_id'], '{')) {
        $variation_info = $products_attributes->get_variation_from_attrib($product_info['products_id']);
        $products_name = $product_info['products_name'] . $variation_info['product_name_suffix'];
    } else {
        $variation_info = $products_attributes->get_current_selected_variation();
        if (@tep_not_null($variation_info)) {
            $products_name = $product_info['products_name'] . $variation_info['product_name_suffix'];
        } else {
            $products_name = $product_info['products_name'];
        }
        $products_url = tep_href_link('product_info.php', 'products_id=' . $product_info['products_id'], 'SSL', false);
        $products_name = "<a href='{$products_url}' itemprop='url'><span id='products_name' itemprop='name'>{$products_name}</span></a>";
        // $products_name = sprintf(MODULE_CONTENT_PI_NAME_DISPLAY_NAME, $products_name);
        return "<div hx-swap-oob='true' class='col-sm-{$content_width} cm-pi-name' id='cm-pi-name' style='min-height:70px'><div class='page-header'><h1 class='h1'>{$products_name}</h1></div></div>";
    }
}

function tcs_subscriptions_searchBox(){
    return tcs_draw_form_element([
        "type" => "input",
        "sub_type" => "search",
        "name" => "subscription_search",
        "placeholder"=>"Begin Typing To Search ...", 
        "hx-post"=>"api/autodesk_search_subscriptions_table",
        "hx-trigger"=>"input changed delay:500ms, search",
        "hx-target"=>"#subscriptions_body",
        "hx-indicator"=>"#indicatorLines",
        "hx-vals" => [
            "action" => "autodesk_search_subscriptions_table"
        ]
    ]);
}