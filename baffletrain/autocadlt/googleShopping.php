<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2010 osCommerce

  Released under the GNU General Public License
*/

	require('includes/application_top.php');
	require('includes/functions/googleCategoryfunctions.php');
	require('ext/Google/ProductsSample.php');
	require('ext/Google/ProductstatusesSample.php');
	$action = (isset($_GET['action']) ? $_GET['action'] : '');

	switch ($action) {
		case 'googleUpload':			
			$googleUpload = new googleProductUpload();
				$googleUpload->googleLog->lwrite('===========================================================',true);
				$googleUpload->googleLog->lwrite('starting upload, creating object');
				$googleUpload->googleLog->lwrite('starting upload, doing');
				$googleUpload->googleLog->lwrite('=========================================================');
			$googleUploadBatch = $googleUpload->batchUpload();
			$googleUpload->googleLog->lwrite('=============================================================');
				$googleUpload->googleLog->lwrite('starting download, creating');
				$googleDownload->googleLog->lwrite('=========================================================');
			$googleDownload =  new productStatuses();
				$googleUpload->googleLog->lwrite('starting download, doing');
				$googleDownload->googleLog->lwrite('=========================================================');
			$googleDownload->updateOnlineProducts();
			$googleDownload->googleLog->lwrite('=========================================================');
			$googleUpload->googleLog->lwrite('Product Upload Complete');
				$googleUpload->googleLog->lclose();
				echo 'Complete';
			
		break;
		case 'deleteAll':
				echo '<br><br>starting<br><br>';
			$googleDownload =  new googleProductUpload();
				$googleDownload->googleLog->lwrite('==============================================');
				$googleDownload->googleLog->lwrite('starting delete, creating object');			
				$googleDownload->googleLog->lwrite('starting delete, doing:');
			$googleDownload->deleteAllProducts();
				$googleDownload->googleLog->lclose();
				echo 'done';
		break;
		default:
			$mode = 'default';
			$sort = (isset($_GET['sort']) ? $_GET['sort'] : null);
			$googleProducts = getGoogleProducts(1,$sort);
				
		break;	
	}
	
	if($mode == 'default'){
  require('includes/template_top.php');
?>

    <table border="0" width="100%" cellspacing="0" cellpadding="2">
      <tr>
        <td width="100%"><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td class="pageHeading"><?php echo HEADING_TITLE; ?></td>
            <td class="pageHeading" align="right"><?php echo tep_draw_separator('pixel_trans.gif', HEADING_IMAGE_WIDTH, HEADING_IMAGE_HEIGHT); ?></td>
          </tr>
        </table></td>
      </tr>
      <tr>
        <td><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td valign="top"><table border="0" width="100%" cellspacing="0" cellpadding="2">
              <tr class="dataTableHeadingRow">
		<td class="dataTableHeadingContent" style="cursor:pointer" onclick="document.location.href='<?php echo tep_href_link('googleShopping.php', 'sort=products_model') ?>'" ><?php echo 'Model'; ?></td>
                <td class="dataTableHeadingContent" style="cursor:pointer" onclick="document.location.href='<?php echo tep_href_link('googleShopping.php', 'sort=products_name') ?>'" ><?php echo 'Product'; ?></td>
                <td class="dataTableHeadingContent" style="cursor:pointer" onclick="document.location.href='<?php echo tep_href_link('googleShopping.php', 'sort=google_status_level') ?>'" ><?php echo 'Status'; ?></td>
                <td class="dataTableHeadingContent" style="cursor:pointer" onclick="document.location.href='<?php echo tep_href_link('googleShopping.php', 'sort=google_status_message') ?>'" > <?php echo 'Error'; ?></td>
                <td class="dataTableHeadingContent" align="right"><?php echo 'I'; ?></td>
              </tr>
   <?php
				foreach ($googleProducts as $googleProduct) {
					if(!empty($googleProduct['google_status_level'])){
						$levels = explode('|',$googleProduct['google_status_level']);
						$messages = explode('|',$googleProduct['google_status_message']);
						foreach ($levels as $key => $level){
							if((empty($level)) || ($level == 'auth/frontend/not_claimed') || $level == 'missing_condition_microdata' 	){}else{
								if (isset($sInfo) && is_object($sInfo) && ($googleProduct['google_id'] == $sInfo->google_id)) {
									echo '                  <tr id="defaultSelected" class="dataTableRowSelected" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link('googleShopping.php', 'page=' . $_GET['page'] . '&sID=' . $product['products_id']. '&action=edit') . '\'">' . "\n";
								} else {
									echo '                  <tr class="dataTableRow" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link('googleShopping.php', 'page=' . $_GET['page'] . '&sID=' . $googleProduct['products_id']) . '\'">' . "\n";
								}
							?>
											<td  class="dataTableContent" valign="top" style="white-space: nowrap;" ><?php echo $googleProduct['products_model']; ?></td>
											<td  class="dataTableContent" valign="top" style="white-space: nowrap;"><?php echo $googleProduct['products_name']; ?></td>
											<td  class="dataTableContent" valign="top"  style="white-space: nowrap;"><?php echo $level; ?></td>
											<td  class="dataTableContent" valign="top"  style="white-space: nowrap;"><?php echo $messages[$key] ?></td>
											<td  class="dataTableContent" valign="top"  style="white-space: nowrap;"align="right">
							<?php
								  if ($googleProduct['status_inserted'] == '1') {
									echo tep_image('images/icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10);
								  } else {
									echo tep_image('images/icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10);
								  }
							?></td>
							<?php //	<td class="dataTableContent" align="right"><?php if (isset($sInfo) && is_object($sInfo) && ($googleProduct['google_id'] == $sInfo->google_id)) { echo tep_image('images/icon_arrow_right.gif', ''); } else { echo '<a href="' . tep_href_link('googleShopping.php', 'page=' . $_GET['page'] . '&sID=' . $googleProduct['google_id']) . '">' . tep_image('images/icon_info.gif', IMAGE_ICON_INFO) . '</a>'; }&nbsp;</td> ?>
								  </tr>
							<?php
							}
						}
					}
				}
?></td>
                  </tr>
<?php
  if (empty($action)) {
?>
                  <tr>
                    <td class="smallText" colspan="2" align="right"><?php echo tep_draw_button(IMAGE_NEW_PRODUCT, 'plus', tep_href_link('googleShopping.php', 'page=' . $_GET['page'] . '&action=new')); ?></td>
                  </tr>
<?php
  }
?>
                </table></td>
              </tr>
            </table></td>
<?php
  $heading = array();
  $contents = array();

      if (is_object($sInfo)) {
        $heading[] = array('text' => '<strong>' . $sInfo->products_name . '</strong>');

        $contents[] = array('align' => 'center', 'text' => tep_draw_button(IMAGE_EDIT, 'document', tep_href_link('googleShopping.php', 'page=' . $_GET['page'] . '&sID=' . $sInfo->google_id . '&action=edit')) . tep_draw_button(IMAGE_DELETE, 'trash', tep_href_link('googleShopping.php', 'page=' . $_GET['page'] . '&sID=' . $sInfo->google_id . '&action=delete')));
        $contents[] = array('text' => '<br />' . TEXT_INFO_DATE_ADDED . ' ' . tep_date_short($sInfo->google_date_added));
        $contents[] = array('text' => '' . TEXT_INFO_LAST_MODIFIED . ' ' . tep_date_short($sInfo->google_last_modified));
        $contents[] = array('align' => 'center', 'text' => '<br />' . tep_info_image($sInfo->products_image, $sInfo->products_name, SMALL_IMAGE_WIDTH, SMALL_IMAGE_HEIGHT));
        $contents[] = array('text' => '<br />' . TEXT_INFO_ORIGINAL_PRICE . ' ' . $currencies->format($sInfo->products_price));
        $contents[] = array('text' => '' . TEXT_INFO_NEW_PRICE . ' ' . $currencies->format($sInfo->google_new_products_price));
        $contents[] = array('text' => '' . TEXT_INFO_PERCENTAGE . ' ' . number_format(100 - (($sInfo->google_new_products_price / $sInfo->products_price) * 100)) . '%');

        $contents[] = array('text' => '<br />' . TEXT_INFO_EXPIRES_DATE . ' <strong>' . tep_date_short($sInfo->expires_date) . '</strong>');
        $contents[] = array('text' => '' . TEXT_INFO_STATUS_CHANGE . ' ' . tep_date_short($sInfo->date_status_change));
      }
 
	  if ( (@tep_not_null($heading)) && (@tep_not_null($contents)) ) {
		echo '            <td width="25%" valign="top">' . "\n";

		$box = new box;
		echo $box->infoBox($heading, $contents);

		echo '            </td>' . "\n";
	  }

?>
          </tr>
        </table></td>
      </tr>
    </table>

	
<?php

/*
		<table width="100%" cellspacing="0" cellpadding="0" border="0"><tbody><tr><td>
		<?php
		
		echo '<table width="100%" cellspacing="0" cellpadding="2" border="0"><tbody>';
		echo '<tr class="dataTableHeadingRow"><td class="dataTableHeadingContent">Products Name</td><td class="dataTableHeadingContent">status_level</td><td class="dataTableHeadingContent">status_message</td><td class="dataTableHeadingContent">last_updated_date</td></tr>';
foreach ($productList as $product) {
			echo '<tr class="dataTableRow"><td class="dataTableContent">' . $product['products_name'] . '</td><td class="dataTableContent">' . $product['status_level']  . '</td><td class="dataTableContent">' . $product['status_message']  . '</td><td class="dataTableContent">' . $product['last_updated_date'] . '</td></tr>';
		}
		echo '</tbody></table>';
		?>
		</td></tr></table>	*/


	}
 
  require('includes/template_bottom.php');
  require('includes/application_bottom.php');
?>
