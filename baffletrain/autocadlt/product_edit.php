<?php
/*
  $Id$

osCommerce, Open Source E-Commerce Solutions
http://www.oscommerce.com

Copyright (c) 2015 osCommerce

Released under the GNU General Public License
*/

require('includes/application_top.php');

require('includes/classes/currencies.php');
$currencies = new currencies();

include('includes/functions/tcs_attributes_components.php');

$products_blockFreeShip_yes = false;
$products_blockFreeShip_no  = true;
$products_digiProduct_yes = false;
$products_digiProduct_no  = true;
$products_request_quote_yes = false;
$products_request_quote_no  = true;

function console_log($output, $with_script_tags = true)
{
    $js_code = 'console.log(' . json_encode($output, JSON_HEX_TAG) . ');';
    if ($with_script_tags) {
        $js_code = '<script>' . $js_code . '</script>';
    }
    echo $js_code;
}


function tep_get_products_video_url($products_id, $language_id)
{
    $product_query = tep_db_query("select products_video_url from products_description where products_id = '" . (int) $products_id . "' and language_id = '" . (int) $language_id . "'");
    $product       = tep_db_fetch_array($product_query);

    return $product['products_video_url'];
}


function tep_get_models($models_array = '') // Function modified from tep_get_manufacturers()
{
    global $language, $first, $last;
    if (!is_array($models_array))
        $models_array = array();
    $models_query = tep_db_query("SELECT products_id,
                                         products_model 
                                  FROM products 
                                  ORDER BY products_model");
    $count        = 0;
    while ($models = tep_db_fetch_array($models_query)) {
        if ($count == 0) {
            $first = $models['products_model'];
        }
        $models_array[] = array(
            'id' => $models['products_id'],
            'text' => $models['products_model']
        );
        $count++;
        $last = $models['products_model'];
    }
    return $models_array;
}
// end mark

$action = (isset($_GET['action']) ? $_GET['action'] : '');
$debug = 'start_';
if (@tep_not_null($action)) {
    // Ultimate SEO URLs v2.2d
    // If the action will affect the cache entries
    if (preg_match("/(insert|update|setflag)/i", $action))
        include_once('includes/reset_seo_cache.php');
    switch ($action) {
            //sort order
        case 'insert_product':
        case 'update_product':
            if (isset($_GET['pID']))
                $products_id = tep_db_prepare_input($_GET['pID']);
            $products_date_available = tep_db_prepare_input($_POST['products_date_available']);

            $products_date_available = (date('Y-m-d') < $products_date_available) ? $products_date_available : 'null';
            $price = $_POST['products_price'];
            if (tep_db_prepare_input($_POST['manufacturers_id']) == '15') {
                $autodeskapi = new AutodeskAPI();
                $price = $autodeskapi->get_product_price($products_id);
                if ($price == 0) {
                    $price = $_POST['products_price'];
                }
            }
            $sql_data_array                  = array(
                'products_quantity' => (int) tep_db_prepare_input($_POST['products_quantity']),
                'products_model' => tep_db_prepare_input($_POST['products_model']),
                'products_price' => tep_db_prepare_input($price),
                'products_date_available' => $products_date_available,
                'products_weight' => (float) tep_db_prepare_input($_POST['products_weight']),
                'products_status' => tep_db_prepare_input($_POST['products_status']),
                'products_google_status' => tep_db_prepare_input($_POST['products_google_status']),
                'products_tax_class_id' => tep_db_prepare_input($_POST['products_tax_class_id']),
                'products_sort_order' => tep_db_prepare_input($_POST['products_sort_order']),
                'products_request_quote' => (int) tep_db_prepare_input($_POST['products_request_quote']),
                'manufacturers_id' => (int) tep_db_prepare_input($_POST['manufacturers_id'])
            );
            $sql_data_array['products_gtin'] = (@tep_not_null($_POST['products_gtin'])) ? str_pad(tep_db_prepare_input($_POST['products_gtin']), 14, '0', STR_PAD_LEFT) : 'null';
            $full_data_array = $sql_data_array;

            $products_image = new upload('products_image');
            $products_image->set_destination(DIR_FS_CATALOG_IMAGES);
            if ($products_image->parse() && $products_image->save()) {
                $sql_data_array['products_image'] = tep_db_prepare_input($products_image->filename);
            }
            $full_data_array['products_image_filename'] = tep_db_prepare_input($_POST['products_image_filename']);
            //echo 'fdr: ' . $full_data_array['products_image_filename'];
            //delete image thumbnails
            foreach (glob(DIR_FS_CATALOG_IMAGES . 'thumbs/*/' . $products_image->filename) as $filename) {
                @unlink($filename);
            }
            // end delete image thumbnails



            if ($action == 'insert_product') {
                $insert_sql_data = array(
                    'products_date_added' => 'now()'
                );

                $sql_data_array = array_merge($sql_data_array, $insert_sql_data);
                $full_data_array = array_merge($sql_data_array, $full_data_array);
                tep_db_perform(TABLE_PRODUCTS, $sql_data_array);
                $products_id = tep_db_insert_id();

                tep_db_query("insert into products_to_categories (products_id, categories_id) values ('" . (int) $products_id . "', '" . (int) $current_category_id . "')");
            } elseif ($action == 'update_product') {
                $update_sql_data = array(
                    'products_last_modified' => 'now()'
                );

                $sql_data_array = array_merge($sql_data_array, $update_sql_data);
                $full_data_array = array_merge($sql_data_array, $full_data_array);
                tep_db_perform(TABLE_PRODUCTS, $sql_data_array, 'update', "products_id = '" . (int) $products_id . "'");
            }

            $languages = tep_get_languages();
            for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                $language_id     = $languages[$i]['id'];
                /* cleanup  */
                $productURL      = str_replace(array(
                    'http://',
                    'https://'
                ), '', $_POST['products_url'][$language_id]);
                $productVideoURL = str_replace(array(
                    'http://',
                    'https://'
                ), '', $_POST['products_video_url'][$language_id]);

                $sql_data_array                             = array(
                    'products_name' => tep_db_prepare_input($_POST['products_name'][$language_id]),
                    'products_description' => tep_db_prepare_input($_POST['products_description'][$language_id]),
                    'products_url' => tep_db_prepare_input($productURL)
                );
                $sql_data_array['products_seo_description'] = tep_db_prepare_input($_POST['products_seo_description'][$language_id]);
                $sql_data_array['products_seo_keywords']    = tep_db_prepare_input($_POST['products_seo_keywords'][$language_id]);
                $sql_data_array['products_seo_title']       = tep_db_prepare_input($_POST['products_seo_title'][$language_id]);
                $sql_data_array['products_video_url']       = tep_db_prepare_input($productVideoURL);

                if ($action == 'insert_product') {
                    $insert_sql_data = array(
                        'products_id' => $products_id,
                        'language_id' => $language_id
                    );

                    $sql_data_array = array_merge($sql_data_array, $insert_sql_data);
                    $full_data_array = array_merge($sql_data_array, $full_data_array);
                    tep_db_perform(TABLE_PRODUCTS_DESCRIPTION, $sql_data_array);
                } elseif ($action == 'update_product') {
                    $full_data_array = array_merge($sql_data_array, $full_data_array);
                    tep_db_perform(TABLE_PRODUCTS_DESCRIPTION, $sql_data_array, 'update', "products_id = '" . (int) $products_id . "' and language_id = '" . (int) $language_id . "'");
                }
            }
            // start indvship
            $tmp = tep_db_prepare_input($_POST['products_ship_price']);
            if (is_numeric($tmp))    $tmp_products_ship_price = round($tmp);
            $tmp = tep_db_prepare_input($_POST['products_ship_qty']);
            if (is_numeric($tmp))    $tmp_products_ship_qty = round($tmp);
            if ($tmp_products_ship_qty == 0) {
                $tmp_products_ship_qty = null;
            };
            // end indvship

            /*  $pi_sort_order = 0;
            $piArray       = array(
                0
            );
			foreach ($_POST as $key => $value) {
                // Update existing large product images
                if (preg_match('/^products_image_large_([0-9]+)$/', $key, $matches)) {
                    $pi_sort_order++;
                    
                    $sql_data_array = array(
                        'htmlcontent' => tep_db_prepare_input($_POST['products_image_htmlcontent_' . $matches[1]]),
                        'sort_order' => $pi_sort_order
                    );
                    
                    $t = new upload($key);
                    $t->set_destination(DIR_FS_CATALOG_IMAGES);
                    if ($t->parse() && $t->save()) {
                        $sql_data_array['image'] = tep_db_prepare_input($t->filename);
                    }
                    $full_data_array = array_merge($sql_data_array,$full_data_array);
                    tep_db_perform(TABLE_PRODUCTS_IMAGES, $sql_data_array, 'update', "products_id = '" . (int) $products_id . "' and id = '" . (int) $matches[1] . "'");
                    
                    $piArray[] = (int) $matches[1];
                } elseif (preg_match('/^products_image_large_new_([0-9]+)$/', $key, $matches)) {
                    // Insert new large product images
                    $sql_data_array = array(
                        'products_id' => (int) $products_id,
                        'htmlcontent' => tep_db_prepare_input($_POST['products_image_htmlcontent_new_' . $matches[1]])
                    );
                    
                    $t = new upload($key);
                    $t->set_destination(DIR_FS_CATALOG_IMAGES);
                    if ($t->parse() && $t->save()) {
                        $pi_sort_order++;
                        
                        $sql_data_array['image']      = tep_db_prepare_input($t->filename);
                        $sql_data_array['sort_order'] = $pi_sort_order;
                        $full_data_array = array_merge($sql_data_array,$full_data_array);
                        tep_db_perform(TABLE_PRODUCTS_IMAGES, $sql_data_array);
                        
                        $piArray[] = tep_db_insert_id();
                    }
                }
            }
          
            $product_images_query = tep_db_query("select image from products_images where products_id = '" . (int) $products_id . "' and id not in (" . implode(',', $piArray) . ")");
            if (tep_db_num_rows($product_images_query)) {
                while ($product_images = tep_db_fetch_array($product_images_query)) {
                    $duplicate_image_query = tep_db_query("select count(*) as total from products_images where image = '" . tep_db_input($product_images['image']) . "'");
                    $duplicate_image       = tep_db_fetch_array($duplicate_image_query);
                    
                    if ($duplicate_image['total'] < 2) {
                        if (file_exists(DIR_FS_CATALOG_IMAGES . $product_images['image'])) {
                            @unlink(DIR_FS_CATALOG_IMAGES . $product_images['image']);
                        }
                    }
                }
                
                tep_db_query("delete from products_images where products_id = '" . (int) $products_id . "' and id not in (" . implode(',', $piArray) . ")");
            }*/
            //indvship
            $sql_shipping_array    = array(
                'products_ship_key' => tep_db_prepare_input($_POST['products_ship_key']),
                'products_ship_methods_id' => tep_db_prepare_input($_POST['products_ship_methods_id']),
                'products_ship_price' => $tmp_products_ship_price,
                'products_ship_qty' => $tmp_products_ship_qty,
                'products_ship_flags' => tep_db_prepare_input($_POST['products_ship_flags_blockFreeShip']) . ',' . tep_db_prepare_input($_POST['products_ship_flags_digiProduct'])
            );
            $sql_shipping_id_array = array(
                'products_id' => (int) $products_id
            );
            $products_ship_query   = tep_db_query("SELECT * FROM products_shipping WHERE products_id = " . (int) $products_id);
            if (tep_db_num_rows($products_ship_query) > 0) {
                if (($_POST['products_ship_key'] == '') && ($_POST['products_ship_methods_id'] == '') && ($_POST['products_ship_price'] == '') && ($_POST['products_ship_qty'] == '') && ($_POST['products_ship_flags'] == '')) {
                    tep_db_query("DELETE FROM products_shipping where products_id = '" . (int) $products_id . "'");
                } else {
                    tep_db_perform('products_shipping', $sql_shipping_array, 'update', "products_id = '" . (int) $products_id . "'");
                }
            } else {
                if (($_POST['products_ship_key'] != '') || ($_POST['products_ship_methods_id'] != '') || ($_POST['products_ship_price'] != '') || ($_POST['products_ship_qty'] != '') || ($_POST['products_ship_flags_blockFreeShip'] != '') || ($_POST['products_ship_flags_digiProduct'] != '')) {
                    $sql_ship_array = array_merge($sql_shipping_array, $sql_shipping_id_array);
                    tep_db_perform('products_shipping', $sql_ship_array, 'insert');
                }
            }
            // end indvship 
            $full_data_array = array_merge($sql_data_array, $full_data_array);
            $full_data_array = array_merge($sql_shipping_id_array, $full_data_array);
            if (USE_CACHE == 'true') {
                tep_reset_cache_block('categories');
                tep_reset_cache_block('also_purchased');
            }



            // $tempLog->lwrite('Categories: Done ');
            tep_redirect(tep_href_link('categories.php', 'cPath=' . $cPath . '&pID=' . $products_id));
            break;
    }
}

// check if the catalog image directory exists
if (is_dir(DIR_FS_CATALOG_IMAGES)) {
    if (!tep_is_writable(DIR_FS_CATALOG_IMAGES))
        $messageStack->add(ERROR_CATALOG_IMAGE_DIRECTORY_NOT_WRITEABLE, 'error');
} else {
    $messageStack->add(ERROR_CATALOG_IMAGE_DIRECTORY_DOES_NOT_EXIST, 'error');
}

require('includes/template_top.php');


// ---- Mark
if ($action == 'new_product') {
    $parameters                             = array(
        'products_name' => '',
        'products_description' => '',
        'products_url' => '',
        'products_id' => '',
        'products_quantity' => '',
        'products_model' => '',
        'products_image' => '',
        'products_larger_images' => array(),
        'products_price' => '',
        'products_weight' => '',
        'products_date_added' => '',
        'products_last_modified' => '',
        'products_date_available' => '',
        'products_status' => '',
        'products_google_status' => '',
        'products_tax_class_id' => '',
        'products_video_url' => '',
        'products_sort_order' => '',
        'products_request_quote' => '',
        'manufacturers_id' => ''
    );
    $parameters['products_gtin']            = '';
    $parameters['products_seo_description'] = '';
    $parameters['products_seo_keywords']    = '';
    $parameters['products_seo_title']       = '';

    $pInfo = new objectInfo($parameters);

    if (isset($_GET['pID']) && empty($_POST)) {
        //$product_query = tep_db_query("select pd.products_name, pd.products_description, pd.products_url, p.products_id, p.products_quantity, p.products_model, p.products_image, p.products_price, p.products_weight, p.products_date_added, p.products_last_modified, date_format(p.products_date_available, '%Y-%m-%d') as products_date_available, p.products_status, p.products_tax_class_id, p.manufacturers_id, p.products_gtin from products p, products_description pd where p.products_id = '" . (int)$_GET['pID'] . "' and p.products_id = pd.products_id and pd.language_id = '" . (int)$languages_id . "'");
        $product_query = tep_db_query("select pd.products_name, pd.products_description, pd.products_url, p.products_id, p.products_quantity, p.products_model, p.products_image, p.products_price, p.products_weight, p.products_date_added, p.products_last_modified, date_format(p.products_date_available, '%Y-%m-%d') as products_date_available, p.products_status, p.products_google_status, p.products_sort_order, p.products_tax_class_id, p.manufacturers_id, p.products_gtin, p.products_request_quote, pd.products_video_url from products p, products_description pd where p.products_id = '" . (int) $_GET['pID'] . "' and p.products_id = pd.products_id and pd.language_id = '" . (int) $languages_id . "'");
        $product       = tep_db_fetch_array($product_query);

        $pInfo->objectInfo($product);
        // start indvship
        $products_shipping_query = tep_db_query("SELECT * FROM products_shipping WHERE products_id=" . (int) $_GET['pID']);
        while ($products_shipping = tep_db_fetch_array($products_shipping_query)) {
            $products_ship_key        = $products_shipping['products_ship_key'];
            $products_ship_methods_id = $products_shipping['products_ship_methods_id'];
            $products_ship_price      = $products_shipping['products_ship_price'];
            $products_ship_qty        = $products_shipping['products_ship_qty'];
            $products_ship_flags      = $products_shipping['products_ship_flags'];
        }
        $shipping = array(
            'products_ship_methods_id' => $products_ship_methods_id,
            'products_ship_key' => $products_ship_key,
            'products_ship_price' => $products_ship_price,
            'products_ship_qty' => $products_ship_qty,
            'products_ship_flags' => $products_ship_flags
        );
        $pInfo->objectInfo($shipping);
        // end indvship


        if (!isset($pInfo->products_status)) {
            $pInfo->products_status = '1';
        }
        if (!isset($pInfo->products_google_status)) {
            $pInfo->products_google_status = '1';
        }
        $sFlagsA = preg_split("/[:,]/", $products_ship_flags);
        switch ($sFlagsA[0]) {
            case '1':
                $products_blockFreeShip_yes = true;
                $products_blockFreeShip_no  = false;
                break;
            case '0':
            default:
                $products_blockFreeShip_yes = false;
                $products_blockFreeShip_no  = true;
        }
        switch ($sFlagsA[1]) {
            case '1':
                $products_digiProduct_yes = true;
                $products_digiProduct_no  = false;
                break;
            case '0':
            default:
                $products_digiProduct_yes = false;
                $products_digiProduct_no  = true;
        }
        switch ($product['products_request_quote']) {
            case '1':
                $products_request_quote_yes = true;
                $products_request_quote_no  = false;
                break;
            case '0':
            default:
                $products_request_quote_yes = false;
                $products_request_quote_no  = true;
        } // end indvship
        $product_images_query = tep_db_query("select id, image, htmlcontent, sort_order from products_images where products_id = '" . (int) $product['products_id'] . "' order by sort_order");
        while ($product_images = tep_db_fetch_array($product_images_query)) {
            $pInfo->products_larger_images[] = array(
                'id' => $product_images['id'],
                'image' => $product_images['image'],
                'htmlcontent' => $product_images['htmlcontent'],
                'sort_order' => $product_images['sort_order']
            );
        }
    }

    $manufacturers_array = array(
        array(
            'id' => '',
            'text' => TEXT_NONE
        )
    );
    $manufacturers_query = tep_db_query("select manufacturers_id, manufacturers_name from manufacturers order by manufacturers_name");
    while ($manufacturers = tep_db_fetch_array($manufacturers_query)) {
        $manufacturers_array[] = array(
            'id' => $manufacturers['manufacturers_id'],
            'text' => $manufacturers['manufacturers_name']
        );
    }

    $tax_class_array = array(
        array(
            'id' => '0',
            'text' => TEXT_NONE
        )
    );
    $tax_class_query = tep_db_query("select tax_class_id, tax_class_title from tax_class order by tax_class_title");
    while ($tax_class = tep_db_fetch_array($tax_class_query)) {
        $tax_class_array[] = array(
            'id' => $tax_class['tax_class_id'],
            'text' => $tax_class['tax_class_title']
        );
    }

    $languages = tep_get_languages();

    if (!isset($pInfo->products_status)) {
        $pInfo->products_status = '1';
    }
    switch ($pInfo->products_status) {
        case '0':
            $in_status  = false;
            $out_status = true;
            break;
        case '1':
        default:
            $in_status  = true;
            $out_status = false;
    }

    if (!isset($pInfo->products_google_status)) {
        $pInfo->products_google_status = '1';
    }
    switch ($pInfo->products_google_status) {
        case '0':
            $in_google_status  = false;
            $out_google_status = true;
            break;
        case '1':
        default:
            $in_google_status = true;
            $out_google_status = false;
    }

    $form_action = (isset($_GET['pID'])) ? 'update_product' : 'insert_product'; ?>



    <script type="text/javascript">
        <!--
        var tax_rates = new Array();
        <?php
        for ($i = 0, $n = sizeof($tax_class_array); $i < $n; $i++) {
            if ($tax_class_array[$i]['id'] > 0) {
                echo 'tax_rates["' . $tax_class_array[$i]['id'] . '"] = ' . tep_get_tax_rate_value($tax_class_array[$i]['id']) . ';' . "\n";
            }
        } ?>

        function doRound(x, places) {
            return Math.round(x * Math.pow(10, places)) / Math.pow(10, places);
        }

        function getTaxRate() {
            var selected_value = document.forms["new_product"].products_tax_class_id.selectedIndex;
            var parameterVal = document.forms["new_product"].products_tax_class_id[selected_value].value;

            if ((parameterVal > 0) && (tax_rates[parameterVal] > 0)) {
                return tax_rates[parameterVal];
            } else {
                return 0;
            }
        }

        function updateGross() {
            var taxRate = getTaxRate();
            var grossValue = document.forms["new_product"].products_price.value;

            if (taxRate > 0) {
                grossValue = grossValue * ((taxRate / 100) + 1);
            }

            document.forms["new_product"].products_price_gross.value = doRound(grossValue, 4);
        }

        function updateNet() {
            var taxRate = getTaxRate();
            var netValue = document.forms["new_product"].products_price_gross.value;

            if (taxRate > 0) {
                netValue = netValue / ((taxRate / 100) + 1);
            }

            document.forms["new_product"].products_price.value = doRound(netValue, 4);
        }
        //
        -->
    </script>
    <?php echo tep_draw_form('new_product', 'product_edit.php', 'cPath=' . $cPath . (isset($_GET['pID']) ? '&pID=' . $_GET['pID'] : '') . '&action=' . $form_action, 'post', 'enctype="multipart/form-data" id="new_product_form"'); ?>
    <input type="hidden" name="products_id" id="products_id_hidden_input" value="<?php echo $_GET['pID'] ?>" />
    <input type="hidden" name="cPath" id="cPath_hidden_input" value="<?php echo $_GET['pID'] ?>" />


    <table border="0" width="90%" cellspacing="0" cellpadding="2">
        <tr>
            <td colspan="4">
                <table border="0" width="100%" cellspacing="0" cellpadding="0">
                    <tr>
                        <td class="pageHeading" colspan="4"><?php echo sprintf(TEXT_NEW_PRODUCT, tep_output_generated_category_path($current_category_id)); ?></td>
                        <td class="pageHeading" align="right"><?php echo tep_draw_separator('pixel_trans.gif', HEADING_IMAGE_WIDTH, HEADING_IMAGE_HEIGHT); ?></td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
        </tr>

        <tr>
            <td class="main"></td>


            <?php
            if (@tep_not_null($pInfo->products_image)) {
                $pi_query = tep_db_query("select image, htmlcontent from products_images where products_id = '" . (int) $pInfo->products_id . "' order by sort_order");
            } ?>

            <?php
            for ($i = 0, $n = sizeof($languages); $i < $n; $i++) { ?>

        <tr bgcolor="#eeeeee">
            <td class="main">Seo Title</td>
            <td class="main" colspan=4><?php echo tep_draw_input_field('products_seo_title[' . $languages[$i]['id'] . ']', (empty($pInfo->products_id) ? '' : tep_get_products_seo_title($pInfo->products_id, $languages[$i]['id'])), 'style="width: 500px;"'); ?></td>
        </tr>
    <?php
            } ?>
    <tr>
        <td class="main"><?php echo TEXT_PRODUCTS_MANUFACTURER; ?></td>
        <td class="main"><?php echo tep_draw_pull_down_menu('manufacturers_id', $manufacturers_array, $pInfo->manufacturers_id); ?></td>

        <td class="main"><?php echo TEXT_PRODUCTS_MODEL; ?></td>
        <td class="main"><?php echo tep_draw_input_field('products_model', $pInfo->products_model); ?></td>
    </tr>
    <tr>
        <td class="main"><?php echo 'GTIN:'; ?><script type="text/javascript">
                <!--
                //
                -->
            </script>
        </td>
        <?php // for ($i=0, $n=sizeof($languages); $i<$n; $i++) { 
        ?>
        <td class="main"> <?php echo tep_draw_input_field('products_gtin', $pInfo->products_gtin) . ' (UPC, EAN, ISBN or MPN)'; ?></td>
    </tr>
    <tr>
        <td colspan="4" style="max-width:1024px">
            <hr>
        </td>
    </tr>
    <tr>
        <td class="main">
            <script type="text/javascript">
                $(document).ready(function() {
                    if (<?php if ($products_digiProduct_yes) {
                            echo 1;
                        } else {
                            echo 0;
                        } ?>) {
                        $('.indvShip').fadeTo('slow', .25);
                    }
                    $('#products_digiProduct_yes').click(function() {
                        $('.indvShip').fadeTo('slow', .25);
                    });
                    $('#products_digiProduct_no').click(function() {
                        $('.indvShip').fadeTo('slow', 1);
                    });
                });
            </script>

            <?php echo 'Digital Delivery:'; ?>
        </td>

        <td class="main"><?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;No:&nbsp;' . tep_draw_radio_field('products_ship_flags_digiProduct', '0', $products_digiProduct_no, null, 'id="products_digiProduct_no"') . '&nbsp;Yes:&nbsp;' . tep_draw_radio_field('products_ship_flags_digiProduct', '1', $products_digiProduct_yes, null, 'id="products_digiProduct_yes"'); ?></td>

    </tr>
    <?php // start indvship  
    ?>
    <tr class="indvShip">
        <td class="main">Group</td>
        <td class="main" valign=top><?php echo tep_draw_input_field('products_ship_key', $pInfo->products_ship_key);
                                    if (@tep_not_null($pInfo->products_ship_key))
                                        echo 'notnull';
                                    else
                                        echo 'null'; ?></td>
        <td class="main">Current Keywords:</td>
        <td rowspan='4' class="main">
            <table cellspacing="0" cellpadding="2" width="100%" border="0">
                <?php
                $i      = 0;
                $select = tep_db_query("SELECT * FROM `products_shipping` GROUP BY `products_ship_key` ORDER BY `products_ship_key` DESC");
                while ($keywords = tep_db_fetch_array($select)) {
                    $i++;
                    if ($i == 1) {
                        echo "<tr><td>";
                    } else {
                        echo "<td>&nbsp;</td><td>";
                    }
                    echo "<a href='javascript:null();' onclick=\"document.forms['new_product'].products_ship_key.value='" . $keywords['products_ship_key'] . "';document.forms['new_product'].products_ship_price.value='" . $keywords['products_ship_price'] . "';document.forms['new_product'].products_ship_qty.value='" . $keywords['products_ship_qty'] . "';\">" . $keywords['products_ship_key'] . "";
                    if ($i == 1) {
                        echo "</td>";
                    } else {
                        echo "</td></tr>";
                        $i = 0;
                    }
                }
                ?></table>
        </td>
    </tr> <!-- end Zipcode --> <!-- Indvship -->

    <tr class="indvShip">
        <td class="main" style="width:210px"><?php echo 'Indv. Shipping Price:'; ?></td>
        <td class="main"><?php echo tep_draw_input_field('products_ship_price', $pInfo->products_ship_price);
                            if (@tep_not_null($pInfo->products_ship_price))
                                echo 'notnull';
                            else
                                echo 'null'; ?></td>
    </tr>
    <tr class="indvShip">
        <td class="main"><?php echo 'Qty to qualify for free shipping:'; ?></td>
        <td class="main"><?php echo tep_draw_input_field('products_ship_qty', $pInfo->products_ship_qty);
                            if (@tep_not_null($pInfo->products_ship_qty))
                                echo 'notnull';
                            else
                                echo 'null'; ?></td>
    </tr>
    <tr class="indvShip">
        <td class="main">
            <?php echo 'Exempt free shipping > £200?:'; ?></td>

        <td class="main"><?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;No:&nbsp;' . tep_draw_radio_field('products_ship_flags_blockFreeShip', '0', $products_blockFreeShip_no) . '&nbsp;Yes:&nbsp;' . tep_draw_radio_field('products_ship_flags_blockFreeShip', '1', $products_blockFreeShip_yes); ?></td>

        <!-- end Indvship -->


    </tr>
    <tr class="">
        <td colspan="4" style="max-width:930px">
            <hr>
        </td>
    </tr>
    <tr>
        <td class="main"><?php echo TEXT_PRODUCTS_PRICE_NET; ?></td>
        <td class="main"><?php echo tep_draw_input_field('products_price', $pInfo->products_price); ?> </td>
        <td class="main">
            <?php echo 'Show "Request Quote" Button:'; ?></td>
        <td class="main">
            <?php echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;No:&nbsp;' . tep_draw_radio_field('products_request_quote', '0', $products_request_quote_no) . '&nbsp;Yes:&nbsp;' . tep_draw_radio_field('products_request_quote', '1', $products_request_quote_yes); ?></td>


    </tr>
    <tr>
        <td class="main"><?php echo TEXT_PRODUCTS_PRICE_GROSS; ?></td>
        <td class="main"><?php echo tep_draw_input_field('products_price_gross', $pInfo->products_price); ?></td>
        <td class="main"><?php echo TEXT_PRODUCTS_TAX_CLASS; ?></td>
        <td class="main">
            <?php echo tep_draw_pull_down_menu('products_tax_class_id', $tax_class_array, 1, 'id="products_tax_class_id"'); ?></td>


    </tr>
    <tr>
        <td colspan="4" style="max-width:930px">
            <hr>
        </td>
    </tr>
    <script type="text/javascript">
        <!--
        $(document).ready(function() {
            $("input[name=products_price]").keyup(updateGross);
            $("input[name=products_price_gross").keyup(updateNet);
            //  $("[name=products_tax_class_id]").onchange(updateGross);    
            updateGross();
        });

        //
        -->
    </script>
    <?php
    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) { ?>
        <tr>
            <td class="main" valign="top" colspan="4"><?php
                                                        echo TEXT_PRODUCTS_IMAGE; ?></td>

        <tr>

        <?php
    } ?>
        <div id="piGal" style="float: left;max-width:100%;min-width:300px">
            <td colspan="4" class="main">
                <div style="max-width:200px">
                    <?php echo '<br />' . (@tep_not_null($pInfo->products_image) ? '<a href="' . DIR_WS_CATALOG_IMAGES . $pInfo->products_image . '" target="_blank">
			<img class="img-responsive" src="' . DIR_WS_CATALOG_IMAGES . $pInfo->products_image . '"></a>' .
                        tep_draw_hidden_field('products_image_filename', $pInfo->products_image) : '') . tep_draw_file_field('products_image'); ?>
                </div>

        </div>
        <div id="piGal" style="float: left;max-width:100%;min-width:300px">YouTube Video
            <?php for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                echo tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name']) . '&nbsp;' . tep_draw_input_field('products_video_url[' . $languages[$i]['id'] . ']', (isset($products_video_url[$languages[$i]['id']]) ? stripslashes($products_video_url[$languages[$i]['id']]) : tep_get_products_video_url($pInfo->products_id, $languages[$i]['id'])), 'size="55" ');
            } ?>
            <?php
            if (@tep_not_null($pInfo->products_video_url)) { ?>
                <div class="videoWrapper">
                    <div class="video_iframe">
                        <!-- a transparent image is preferable -->
                        <img class="videoRatio" src="<?php
                                                        echo DIR_WS_CATALOG_IMAGES . 'placeholders/16x9.png'; ?>" />
                        <iframe src="https://<?php
                                                echo $pInfo->products_video_url; ?>" frameborder="0" allowfullscreen="1"></iframe>
                    </div>
                </div>
            <?php
            } ?>
        </div>
        </td>


        </tr> <?php
                // end indvship 
                if (isset($pInfo->products_tax_class_id)) {
                    $taxSelection = $pInfo->products_tax_class_id;
                } else {
                    $taxSelection = 1;
                }
                //EPP: Attributes Table
                ?>
        </tr>
        <tr>
            <td colspan="4">

                <?php //EPP: Variations Table
                ?>
        <tr>
            <td colspan="4">

            </td>
        </tr>
        <tr>
            <td colspan="4"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
        </tr>
        <tr>
            <td colspan="4">
                <div class="col-xs-10">
                <?= get_attributes_tables($pInfo->products_id); ?>
                </div>
                <ul id="piList">
                    <?php
                    $pi_counter = 0;

                    foreach ($pInfo->products_larger_images as $pi) {
                        $pi_counter++;

                        echo '                <li id="piId' . $pi_counter . '" class="ui-state-default" style="float:left; max-width:120px">'  .
                            '<span class="ui-icon ui-icon-arrowthick-2-n-s" style="float: right;"></span>' .
                            '<a id="large_image_' . $pi['id'] . '" data-imageid="' . $pi['id'] . '" class="imageDelete ui-icon ui-icon-trash" style="cursor:pointer;float: right;"></a>' .
                            '<strong>Image '  . $pi['id'] . '</strong><br />' .
                            '<a href="' . DIR_WS_CATALOG_IMAGES . $pi['image'] . '" target="_blank"><img class="img-responsive" src="' . DIR_WS_CATALOG_IMAGES . $pi['image'] . '"></a>' .
                            tep_draw_file_field('products_image_large_' . $pi['id'], false, 'style="width: 90px;overflow: hidden;"');

                        //'<br /><br />' . TEXT_PRODUCTS_LARGE_IMAGE_HTML_CONTENT . '<br />' .
                        //tep_draw_textarea_field('products_image_htmlcontent_' . $pi['id'], 'soft', '70', '3', $pi['htmlcontent']) . '</li>';
                    } ?>
                </ul>
                <br style="clear:both">
                <a href="#" style="float: left;"></span><?php echo TEXT_PRODUCTS_ADD_LARGE_IMAGE; ?></a>

                <div id="piDelConfirm" title="<?php echo TEXT_PRODUCTS_LARGE_IMAGE_DELETE_TITLE; ?>">
                    <p><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span><?php echo TEXT_PRODUCTS_LARGE_IMAGE_CONFIRM_DELETE; ?></p>
                </div>

                <style type="text/css">
                    #piList {
                        list-style-type: none;
                        margin: 0;
                        padding: 0;
                    }

                    #piList li {
                        margin: 5px 0;
                        padding: 2px;
                    }
                </style>




                <!-- Add this input field for batch image uploads -->
                <input type="file" id="batchUpload" name="batchUpload[]" multiple />
                <progress id="uploadProgress" value="0" max="100" style="display: none;"></progress>


                <script>
                    $(document).ready(function() {
                        let isUploading = false; // Flag to prevent multiple uploads

                        $('#batchUpload').on('change', function(event) {
                            if (isUploading) return; // Prevent another upload if already in progress

                            isUploading = true; // Set flag to indicate upload is in progress

                            let files = event.target.files;
                            let formData = new FormData();

                            $.each(files, function(index, file) {
                                formData.append('batchUpload[]', file);
                            });

                            // Add additional data if needed
                            formData.append('products_id', <?php echo $pInfo->products_id; ?>);

                            // Create a new XMLHttpRequest
                            let xhr = new XMLHttpRequest();

                            // When the request is complete
                            xhr.onload = function() {
                                isUploading = false; // Reset flag once upload is complete
                                if (xhr.status === 200) {
                                    let imageData = JSON.parse(xhr.responseText);
                                    updateUI(imageData);
                                    progressBar.val(0); // Reset progress bar
                                    progressBar.hide(); // Hide progress bar
                                } else {
                                    console.error('Upload failed:', xhr.status, xhr.statusText);
                                    progressBar.val(0); // Reset progress bar
                                    progressBar.hide(); // Hide progress bar
                                }
                            };

                            // Handle errors
                            xhr.onerror = function() {
                                isUploading = false; // Reset flag on error
                                console.error('Upload error');
                                progressBar.val(0); // Reset progress bar
                                progressBar.hide(); // Hide progress bar
                            };

                            // Show the progress bar
                            let progressBar = $('#uploadProgress');
                            progressBar.show();

                            // Listen for upload progress events
                            xhr.upload.onprogress = function(event) {
                                if (event.lengthComputable) {
                                    let percentComplete = (event.loaded / event.total) * 100;
                                    progressBar.val(percentComplete);
                                }
                            };

                            // Open the connection and send the data
                            xhr.open('POST', 'your-upload-url-here'); // Replace with your upload URL
                            xhr.send(formData);
                        });
                    });

                    $('.imageDelete').on('click', function(event) {

                        var that = $(this);
                        var method = "product_images_removeImage";
                        $.get("api.php?action=" + method + "&products_id=<?php echo $pInfo->products_id ?>&image_id=" + that.data("imageid"), function(data, status) {
                            if (status == "success") {
                                event.stopPropagation();
                                that.parent().remove();
                            }
                        });
                    });


                    // Function to update the UI with the newly uploaded images
                    function updateUI(imageData) {
                        imageData.forEach(function(image) {
                            let htmlContent = `
                            <li id="piId${image.id}" class="ui-state-default" style="float:left; max-width:120px">
                                <span class="ui-icon ui-icon-arrowthick-2-n-s" style="float: right;"></span>
                                <a id="${'large_image_' + image.id});return false;" data-imageid="${image.id}" class="imageDelete ui-icon ui-icon-trash" style="cursor:pointer;float: right;"></a> 
                                <strong>Image ${image.id}</strong><br />
                                <a href="${image.url}" target="_blank"><img class="img-responsive" src="${image.url}"></a>
                                <input type="file" name="products_image_large_${image.id}" style="width: 90px;overflow: hidden;">
                            </li>`;
                            $('#piList').append(htmlContent);
                        });
                    }

                    $('#piList').sortable({
                        containment: 'parent'
                    });

                    var piSize = <?php echo $pi_counter; ?>;

                    function addNewPiForm() {
                        piSize++;

                        $('#piList').append('<li id="piId' + piSize + '" class="ui-state-default"><span class="ui-icon ui-icon-arrowthick-2-n-s" style="float: right;"></span><a href="#" onclick="showPiDelConfirm(' + piSize + ');return false;" class="ui-icon ui-icon-trash" style="float: right;"></a><strong><?php echo TEXT_PRODUCTS_LARGE_IMAGE; ?></strong><br /><input type="file" name="products_image_large_new_' + piSize + '" /><br /><br /><?php echo TEXT_PRODUCTS_LARGE_IMAGE_HTML_CONTENT; ?><br /><textarea name="products_image_htmlcontent_new_' + piSize + '" wrap="soft" cols="70" rows="3"></textarea></li>');
                    }

                    var piDelConfirmId = 0;

                    $('#piDelConfirm').dialog({
                        autoOpen: false,
                        resizable: false,
                        draggable: false,
                        modal: true,
                        buttons: {
                            'Delete': function() {
                                $('#piId' + piDelConfirmId).effect('blind').remove();
                                $(this).dialog('close');
                            },
                            Cancel: function() {
                                $(this).dialog('close');
                            }
                        }
                    });


                    function showPiDelConfirm(piId) {
                        piDelConfirmId = piId;

                        $('#piDelConfirm').dialog('open');
                    }
                </script>
                </div>

        <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
        </tr>

        <tr>
            <td colspan="4">

            </td>
        </tr>
        <tr>
            <td colspan="4"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
        </tr>


        <?php



        for ($i = 0, $n = sizeof($languages); $i < $n; $i++) { ?>
            <tr>
                <td class="main" valign="top"><?php
                                                echo tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name']);

                                                if ($i == 0) echo TEXT_PRODUCTS_DESCRIPTION;

                                                ?></td>
            </tr>
            <tr>
                <td colspan=4>
                    <table border="0" cellspacing="0" cellpadding="0" style="width:1000px">
                        <tr>
                            <td class="main" valign="top">
                            </td>
                            <td class="main">

                                <?php
                                echo tep_draw_textarea_field_ckeditor('products_description[' . $languages[$i]['id'] . ']', 'soft', '117', '40', (empty($pInfo->products_id) ? '' : stripslashes(tep_get_products_description($pInfo->products_id, $languages[$i]['id'])))); ?></td>
                        </tr>
                    </table>
                </td>
            </tr>
        <?php
        } ?>
        <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
        </tr>
        <tr>
            <td class="main"><?php echo TEXT_PRODUCTS_QUANTITY; ?></td>
            <td class="main"><?php echo tep_draw_input_field('products_quantity', $pInfo->products_quantity); ?></td>
        </tr>
        <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
        </tr>

        <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
        </tr>

        <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
        </tr>
        <?php
        for ($i = 0, $n = sizeof($languages); $i < $n; $i++) { ?>
            <tr bgcolor="#eeeeee">
                <td class="main" valign="top"><?php
                                                if ($i == 0)
                                                    echo TEXT_PRODUCTS_SEO_DESCRIPTION; ?></td>
                <td colspan=3>
                    <table border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td class="main" valign="top"><?php
                                                            echo tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name']); ?>&nbsp;</td>
                            <td class="main"><?php
                                                echo tep_draw_textarea_field('products_seo_description[' . $languages[$i]['id'] . ']', 'soft', '70', '15', (empty($pInfo->products_id) ? '' : tep_get_products_seo_description($pInfo->products_id, $languages[$i]['id']))); ?></td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td class="main"><?php
                                    echo 'More Information:'; ?></td>
                <td class="main" colspan=3><?php
                                            echo tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name']) . '&nbsp;' . tep_draw_input_field('products_url[' . $languages[$i]['id'] . ']', tep_get_products_url($pInfo->products_id, $languages[$i]['id']), 'size="100" '); ?></td>
            </tr>
        <?php
        } ?>
        <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
        </tr>

        <?php
        for ($i = 0, $n = sizeof($languages); $i < $n; $i++) { ?>
            <tr bgcolor="#eeeeee">
                <td class="main" valign="top"><?php
                                                if ($i == 0)
                                                    echo TEXT_PRODUCTS_SEO_KEYWORDS; ?></td>
                <td colspan=3>
                    <table border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td class="main" valign="top"><?php
                                                            echo tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name']); ?>&nbsp;</td>
                            <td class="main" valign="top"><?php
                                                            echo tep_draw_input_field('products_seo_keywords[' . $languages[$i]['id'] . ']', tep_get_products_seo_keywords($pInfo->products_id, $languages[$i]['id']), 'placeholder="' . PLACEHOLDER_COMMA_SEPARATION . '" style="width: 300px;"'); ?></td>
                        </tr>
                    </table>
                </td>
            </tr>
        <?php
        }
        ?>
        <tr>
            <td class="main"><?php echo TEXT_PRODUCTS_WEIGHT; ?></td>
            <td class="main"><?php echo tep_draw_input_field('products_weight', $pInfo->products_weight); ?></td>
        </tr>
        <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
        </tr>
        <tr>
            <td class="main"><?php echo TEXT_EDIT_SORT_ORDER; ?></td>
            <td class="main"><?php echo tep_draw_input_field('products_sort_order', $pInfo->products_sort_order, 'size="2"'); ?></td>
        </tr>

        <tr>
            <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
        </tr>

        <tr>
            <?php //EPP: Related Script	 
            ?>
            <td class="main"><?php echo 'Related Products:'; ?></td>
            <td>


                <style>
                    #relatedProductsModuleContainer {
                        border: dotted 2px lightgrey;
                        background-color: darkgrey;
                    }

                    .listsOptionsDelete {
                        color: red;
                        text-align: center;
                        font-weight: bold;
                        font-family: OCR A Std, monospace;
                        font-size: 1.5em;
                        cursor: pointer;
                    }

                    .listsOptionsEdit {
                        color: Green;
                        text-align: center;
                        font-weight: bold;
                        font-family: OCR A Std, monospace;
                        font-size: 1.5em;
                        cursor: pointer;
                    }

                    .relatedProductsRow {
                        display: none;
                    }

                    .listsSOtd {
                        text-align: center;
                    }

                    #attributes_options_id_cont {
                        visibility: hidden;
                    }

                    .portlet {
                        border: dotted 2px #000;
                        padding: 0.3em;
                        display: inline-block;
                        height: 100%;
                        width: 10px;
                        margin: 2px;
                        position: relative;
                    }

                    .portletTD {
                        padding: 0;
                        width: 0px;
                        position: relative;
                    }

                    /* SortableJS drag states */
                    .sortable-ghost {
                        opacity: 0.4;
                        background-color: #f0f0f0;
                    }

                    .sortable-chosen {
                        background-color: #e6f3ff;
                    }

                    .sortable-drag {
                        opacity: 0.8;
                        transform: rotate(5deg);
                    }

                    /* Ensure drag handle is visible and clickable */
                    .portlet {
                        cursor: move;
                        user-select: none;
                        -webkit-user-select: none;
                        -moz-user-select: none;
                        -ms-user-select: none;
                    }

                    .portlet:hover {
                        background-color: #f5f5f5;
                        border-color: #333;
                    }

                    .material-switch>input[type="checkbox"] {
                        display: none;
                    }

                    .material-switch>label {
                        cursor: pointer;
                        height: 0px;
                        position: relative;
                        width: 40px;
                    }

                    .material-switch>label::before {
                        background: rgb(0, 0, 0);
                        box-shadow: inset 0px 0px 10px rgba(0, 0, 0, 0.5);
                        border-radius: 8px;
                        content: '';
                        height: 16px;
                        margin-top: -8px;
                        position: absolute;
                        opacity: 0.3;
                        transition: all 0.4s ease-in-out;
                        width: 40px;
                    }

                    .material-switch>label::after {
                        background: rgb(255, 255, 255);
                        border-radius: 16px;
                        box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.3);
                        content: '';
                        height: 24px;
                        left: -4px;
                        margin-top: -8px;
                        position: absolute;
                        top: -4px;
                        transition: all 0.3s ease-in-out;
                        width: 24px;
                    }

                    .material-switch>input[type="checkbox"]:checked+label::before {
                        background: inherit;
                        opacity: 0.5;
                    }

                    .material-switch>input[type="checkbox"]:checked+label::after {
                        background: inherit;
                        left: 20px;
                    }
                </style>


                <?php //EPP: -- Related table	 
                ?>
        <tr>
            <td id="relatedProducts" colspan=5 class="main">
                <?php
                $lastTable = -1;

                $get_related_products_query = tep_db_query("SELECT * FROM products as p, products_related as r, products_description as d WHERE r.products_related_related_products_id=p.products_id AND d.products_id=p.products_id AND r.products_related_products_id=" . (int) $_GET['pID'] . " ORDER BY r.products_related_table_id, r.products_related_sort_order");



                while ($products_related = tep_db_fetch_array($get_related_products_query)) {
                    ////print_rr($products_related['products_name']);			
                    $related_variations_q = tep_db_query("select * from products_variations_to_products_related WHERE products_id = '" . $products_related['products_id'] . "' AND products_related_id='" . $products_related['products_related_id']  . "'");

                    while ($related_variations = tep_db_fetch_array($related_variations_q)) {
                        $related_variations_status[$related_variations['products_related_id']][$related_variations['products_variations_id']] = $related_variations['status'];
                    }
                    //print_rr($related_variations_status,"select * from products_variations_to_products_related WHERE products_id = '" . $products_related['products_id'] . "' AND products_related_id='" . $products_related['products_related_id']  . "'");
                    if ($lastTable != $products_related['products_related_table_id']) {
                        if ($products_related['products_related_table_id'] > 0) { ?>
                            </tbody>
    </table>
<?php    }
                        ++$lastTable; ?>
<span><strong>Table <?php echo $lastTable ?></strong> </span> <span class="pull-right"> Code: <input class="relatedProductCodeInput" type="text" name="" size="30" maxlength="30" value="<?php echo "relatedProductsModuleContainer-" . $lastTable; ?>"></span>
<table height="1px" id="relatedProductsTable-<?php echo $products_related['products_related_table_id'] ?>" cellspacing="3" cellpadding="3" class="table-draggable table table-striped table-hover table-condensed" width="100%" data-tableid="<?php echo $products_related['products_related_table_id'] ?>">
    <thead class="thead-dark">
        <tr>
            <th></th>
            <th>#</th>
            <th>Product</th>
            <th class="text-right">Action</th>
        </tr>
    </thead>
    <tbody id="relatedProductsTable-<?php echo $products_related['products_related_table_id'] ?>" class="connectedSortable">
    <?php         }    ?>

    <?php

                    $variations = new tcs_product_attributes($products_related['products_id']); ?>
    <?php if ($variations->has_variations) { ?>
        <?php $related_variations_q = tep_db_query("select * from products_variations_to_products_related WHERE products_id = '" . $pInfo->products_id . "' AND products_related_id='" . $products_related['products_related_id']  . "'");
                        $variationList = $variations->get_variations();    ?>
        <tr class="table-active small" id="relatedProductsRow<?php echo $products_related['products_id']; ?>" data-id="<?php echo $products_related['products_id']; ?>" data-relatedId="<?php echo $products_related['products_related_id']; ?>">
            <td class="portletTD">
                <div class="portlet">&nbsp;</div>
            </td>
            <td colspan="3" style="padding:0;">
                <table id="relatedProductsVariationsTable-<?php echo $products_related['products_related_table_id'] . '-' . $products_related['products_id'] ?>" cellspacing="0" cellpadding="3" class="table table-hover table-condensed table-sm" width="100%" data-relatedid="<?php echo $products_related['products_related_table_id'] ?>">
                    <tbody>
                        <tr class="table-active " id="relatedProductsRow<?php echo $products_related['products_id']; ?>" data-id="<?php echo $products_related['products_id']; ?>" data-relatedId="<?php echo $products_related['products_related_id']; ?>">
                            <td style="width:5%" class="relatedProductsSOtd  " style="width:10px"><input class="indexSO relatedProductSOinput" type="text" name="" size="1" maxlength="4" value="<?php echo $products_related['products_variation_sort_order']; ?>"></td>
                            <td style="width:15%" class=""> <?php echo $products_related['products_model']; ?> </td>
                            <td style="width:80%" class="" colspan=2> <?php echo $products_related['products_name']; ?> </td>

                        </tr>
                    </tbody>
                    <tbody>

                        <?php
                        ////print_rr($variationList,'arrrrfffff');
                        foreach ($variationList as $i => $variation) {

                        ?>
                            <tr class="table-active relatedVaritionRow small" id="variationProductsRow<?php echo $products_variation['products_id']; ?>" data-id="<?php echo $products_variation['products_id']; ?>" data-variationId="<?php echo $products_variation['products_variation_id']; ?>">

                                <td style="width:5%" class="variationtd" style="width:10px"></td>
                                <td style="width:15%"> <?php echo $variation['model']; ?> </td>
                                <td style="width:80%"> <?php echo str_replace(', ', ' ', $variation['product_name_suffix']); ?> </td>
                                <td class="variationProductsDelete listsOptionsToggle">
                                    <div class="material-switch pull-right">
                                        <?php
                                        $check_status = @$related_variations_status[$products_related['products_related_id']][$variation['products_variations_id']];
                                        //print_rr($check_status,'check_status');
                                        if (!isset($check_status) || $check_status == 1) {
                                            $check_status_out = " checked ";
                                        } else {
                                            $check_status_out = "";
                                        }
                                        ?>
                                        <input class="relatedVaritionToggles" id="relatedVaritionToggle-<?php echo $products_related['products_id'] . '-' . $variation['products_variations_id']; ?>" name="relatedVaritionToggle-<?php echo $products_related['products_id'] . '-' . $variation['products_variations_id'] . '"' . $check_status_out . ' data-products_id="' . $products_related['products_id'] . '" data-related_id="' . $products_related['products_related_id'] . '" data-variation_id="' . $variation['products_variations_id'] . '"' ?> type="checkbox" />
                                        <label class="relatedVaritionToggleLabels label-success" for="relatedVaritionToggle-<?php echo $products_related['products_id'] . '-' . $variation['products_variations_id']; ?>" <?php echo ' data-products_id="' . $products_related['products_id'] . '" data-related_id="' . $products_related['products_related_id'] . '" data-variation_id="' . $variation['products_variations_id'] . '"' ?>> </label>
                                    </div>
                                </td>
                            </tr>
                        <?php        } ?>
                </table>
            </td>
            <td class="relatedProductsDelete listsOptionsDelete">x</td>
        </tr>
    <?php
                    } else { ?>
        <tr class="table-active small" id="relatedProductsRow<?php echo $products_related['products_id']; ?>" data-id="<?php echo $products_related['products_id']; ?>" data-relatedId="<?php echo $products_related['products_related_id']; ?>">
            <td class="portletTD">
                <div class="portlet">&nbsp;</div>
            </td>
            <td style="width:5%" class="relatedProductsSOtd" style="width:10px"><input class="indexSO relatedProductSOinput" type="text" name="" size="1" maxlength="4" value="<?php echo $products_related['products_related_sort_order']; ?>"></td>
            <td style="width:15%"> <?php echo $products_related['products_model']; ?> </td>
            <td style="width:80%"> <?php echo $products_related['products_name']; ?> </td>
            <td class="relatedProductsDelete listsOptionsDelete">x</td>
        </tr>
    <?php
                    }   ?>
    </td>
    </tr>
<?php
                    $position++;
                }
?>
    </tbody>
</table>

</td>
</tr>
<tr>
    <td id="relatedProductsToolbar" colspan="5" class="main">
        <table cellspacing="3" cellpadding="3" class="table ">
            <tbody>
                <tr>
                    <td align="left" class="text-left">
                        &nbsp;<input id="relatedProductSearchinput" type="text" name="" size="20" maxlength="100" value="">
                        &nbsp;<button id="relatedProductsSearchButton" class="btn btn-primary" type="button">Search</button>&nbsp;
                        &nbsp;<select id="related_products_select" style="width: 400px;"></select>&nbsp;
                        &nbsp;Table:&nbsp;<select id="related_products_table_select">
                            <?php
                            for ($i = 0; $i <= $lastTable; $i++) {
                                echo '<option value="' .  $i . '">' . $i . '</option>';
                            }
                            ?>
                            <option value="999">Add New...</option>
                        </select>&nbsp;
                        &nbsp;<button id="relatedProductsAddProduct" class="btn btn-primary" type="button">Insert</button>&nbsp;
                        &nbsp;<button id="copyRelatedListFrom" class="btn btn-primary" type="button">Get Product List</button>&nbsp;
                        <?php // &nbsp;<button id="linkRelatedListFrom" class="btn btn-primary" type="button">Link</button>&nbsp; 
                        ?>
                        <?php // 	&nbsp;<button id="relatedAddTable" class="btn btn-primary" type="button">Add Table</button>&nbsp;
                        ?>
                    </td>
                </tr>
            </tbody>
        </table>
    </td>
</tr>
<tr>
    <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
</tr>
<tr>
    <?php echo TEXT_PRODUCTS_STATUS; ?></td>
    <td class="main"><?php echo tep_draw_radio_field('products_status', '1', $in_status) . '&nbsp;' . TEXT_PRODUCT_AVAILABLE . '&nbsp;' . tep_draw_radio_field('products_status', '0', $out_status) . '&nbsp;' . TEXT_PRODUCT_NOT_AVAILABLE; ?></td>
    <td class="main">
        <?php echo 'Google Status:'; ?></td>
    <td class="main"><?php echo tep_draw_radio_field('products_google_status', '1', $in_google_status) . '&nbsp;' . 'List' . '&nbsp;' . tep_draw_radio_field('products_google_status', '0', $out_google_status) . '&nbsp;' . 'Do not list'; ?></td>
</tr>
<tr>
    <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
</tr>
<tr>
    <td class="main"><?php echo TEXT_PRODUCTS_DATE_AVAILABLE; ?></td>
    <td class="main"><?php echo tep_draw_input_field('products_date_available', $pInfo->products_date_available, 'id="products_date_available"') . ' <small>(YYYY-MM-DD)</small>'; ?></td>
</tr>
<tr>
    <td colspan="2"><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
</tr>
<tr>
    <td><?php echo tep_draw_separator('pixel_trans.gif', '1', '10'); ?></td>
</tr>
<tr>
    <td class="smallText" align="right"><?php echo tep_draw_hidden_field('products_date_added', (@tep_not_null($pInfo->products_date_added) ? $pInfo->products_date_added : date('Y-m-d'))) . tep_draw_button(IMAGE_SAVE, 'disk', null, 'primary') . tep_draw_button(IMAGE_CANCEL, 'close', tep_href_link('categories.php', 'cPath=' . $cPath . (isset($_GET['pID']) ? '&pID=' . $_GET['pID'] : ''))); ?></td>
</tr>
<tr>
    <td colspan="5">
        <meta charset="UTF-8">
        <title>Product Catalog</title>
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

        <h2>Autodesk Product Catalog</h1>

            <?php
            $query = "select * from products_to_autodesk_catalog p2a JOIN products_autodesk_catalog pac on p2a.unique_hash = pac.unique_hash WHERE p2a.products_id = '" . (int) $pInfo->products_id . "' Limit 1";
            $current_linked_product_query = tep_db_query($query);
            if (tep_db_num_rows($current_linked_product_query) > 0) {
                $current_linked_product = tep_db_fetch_array($current_linked_product_query);
                $link_string = $current_linked_product['id'] . ': ' . $current_linked_product['orderAction'] . ' ' . $current_linked_product['offeringName'] . ' ' .  $current_linked_product['accessModel_description'] . ' ' . $current_linked_product['term_description'] . ' ' . $current_linked_product['specialProgramDiscount_code'];
            } else {
                $link_string = "Not Linked";
            }
            echo '<div >Currently Linked to: <span id="autodesk_catalog_link_status">' . $link_string . '</Span></div>';
            ?>
            <input type="text" name="terms" id="autodesk_catalog_search_input" placeholder="Search for products...">
            <button type="button" id="autodesk_catalog_search_button" hx-trigger="click" hx-get="api_h.php?action=product_autodesk_link_search" hx-target="#autodesk_catalog_link_select" hx-include="#autodesk_catalog_search_input" hx-swap="innerHTML">Search</button>
            <select id="autodesk_catalog_link_select" style="width: 400px;">
                <option default selected value="">Select Product</option>
            </select>
            &nbsp;&gt;&nbsp;
            <button id="autodesk_catalog_link_button" hx-post="api_h.php?action=product_autodesk_link" hx-include="#autodesk_catalog_link_select">Link</button>
            <div id="productList"></div>


            <!--<script>
                $(document).ready(function() {
                    function fetchProducts() {
                        $.ajax({
                            url: 'api.php',
                            method: 'GET',
                            dataType: 'json',
                            success: function(data) {
                                displayProducts(data);
                            }
                        });
                    }

                    function displayProducts(data) {
                        let html = "";
                        data.forEach(product => {
                            if (typeof(product.id) !== 'undefined') {
                                html += `<option data-id="${product.id}" value="${product.id}">${product.orderAction} ${product.offeringName} ${product.accessModel_description} ${product.term_description} ${product.specialProgramDiscount_code}</option>`
                            }
                        });
                        $('#autodesk_catalog_link_select').html(html);
                    }

                    $('#autodesk_catalog_search_button').on('click', function(e) {
                        e.preventDefault();
                        const searchTerm = $('#autodesk_catalog_search_input').val();
                        $.ajax({
                            url: 'api.php',
                            method: 'GET',
                            data: {
                                action: "product_autodesk_link_search",
                                search: searchTerm
                            },
                            dataType: 'json',
                            success: function(data) {
                                displayProducts(data);
                            }
                        });
                    });


                    $('#autodesk_catalog_link_button').on('click', function(e) {
                        e.preventDefault();
                        const products_id_val = $('#products_id_hidden_input').val();
                        const autodesk_catalog_hash_val = $('#autodesk_catalog_link_select').find(":selected").val()
                        $.ajax({
                            url: 'api.php',
                            method: 'GET',
                            data: {
                                action: "product_autodesk_link_setLink",
                                products_id: products_id_val,
                                autodesk_unique_hash: autodesk_catalog_hash_val
                            },
                            dataType: 'json',
                            success: function(response) {
                                $("#autodesk_catalog_link_status").html($('#autodesk_catalog_link_select').find(":selected").html()); // autodesk_catalog_link_status
                            }
                        });
                    });
                });
            </script>-->
    </td>
</tr>
</table>
</td>
</tr>
</table>

<script type="text/javascript">
    $('#products_date_available').datepicker({
        dateFormat: 'yy-mm-dd'
    });
</script>
<style>
    .navbar-form {
        display: flex;
        align-items: center;
        flex-grow: 1;
    }

    .navbar-form .form-control {
        width: auto;
        /* Allows the input to expand */
        flex-grow: 1;
        /* Makes the input fill available space */
    }

    .container-fluid {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
</style>

<nav class="navbar navbar-default navbar-fixed-bottom form-inline">
    <div class="container-fluid">
        <div class="navbar-header">
            <?php   
                        echo tep_draw_button(IMAGE_SAVE, 'disk', null, 'primary');
                    echo tep_draw_hidden_field('products_date_added', (@tep_not_null($pInfo->products_date_added) ? $pInfo->products_date_added : date('Y-m-d')));
            ?>
            <button type="button" class="btn btn-default" id="applyButton">Apply</button>
        </div>
        <div class="navbar-form navbar-left flex-fill"><label><?php echo TEXT_PRODUCTS_NAME; ?></label>
            <?php
            $i = 0;
            echo tep_draw_input_field('products_name[' . $languages[$i]['id'] . ']', (empty($pInfo->products_id) ? '' : tep_get_products_name($pInfo->products_id, $languages[$i]['id'])), 'class="form-control" '); ?>
        </div>
        <div class="btn-group navbar-right">
            <?php echo '<a href="' . HTTPS_SERVER . '\\-p-' .  $pInfo->products_id . '.html" class="btn btn-default" id="publicButton"  target="_blank">Open in website</a>'; ?>
            <?php //echo tep_draw_button(IMAGE_CANCEL, 'close', tep_href_link('categories.php', 'cPath=' . $cPath . (isset($_GET['pID']) ? '&pID=' . $_GET['pID'] : ''))); ?>
        </div>
    </div>
</nav>
</form>

<?php
}


require('includes/template_bottom.php');
require('includes/application_bottom.php'); ?>