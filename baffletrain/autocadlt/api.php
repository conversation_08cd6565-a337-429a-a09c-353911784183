<?php
require('includes/application_top.php');

$action = (isset($_GET['action']) ? $_GET['action'] : (isset($_POST['action']) ? $_POST['action'] : ''));

if (@tep_not_null($action)) {
    switch ($action) {
        case 'dashboard_liveOrder':
            $response = dashboard_liveOrder($_GET['lastOrder']);
            break;
        case 'related_products_addRelatedProduct':
            $response = related_products_addRelatedProduct($_GET['products_id'], $_GET['related_products_id'], $_GET['relatedSortOrder'], $_GET['related_table_id']);
            break;
        case 'related_products_copyRelatedListFrom':
            $response = related_products_copyRelatedListFrom($_GET['source_id'], $_GET['products_id']);
            break;
        case 'related_products_updateSortOrder':
            $response = related_products_updateSortOrder($_GET['jsonData']);
            break;
        case 'related_products_removeRelatedProduct':
            $response = related_products_removeRelatedProduct($_GET['products_related_id']);
            break;
        case 'related_products_searchRelatedProduct':
            $response = related_products_searchRelatedProduct($_GET['search_term']);
            break;
		case 'related_products_variations_toggle':
            $response = related_products_variations_toggle($_GET['products_id'],$_GET['related_id'],$_GET['variation_id'],$_GET['toggle_status']);
        break;
        case 'product_attributes_addToProduct':
            $response = product_attributes_addToProduct($_GET['products_id'], $_GET['options_id'], $_GET['values_id'], $_GET['value_price'], $_GET['price_prefix'], $_GET['attribute_default'], $_GET['dependson_options_id'], $_GET['dependson_values_id']);
            break;
        case 'product_attributes_updateSortOrder':
            $response = product_attributes_updateSortOrder($_GET['jsonData']);
            break;
        case 'product_attributes_removeAttribute':
            $response = product_attributes_removeAttribute($_GET['products_attributes_id']);
            break;
        case 'product_attributes_getValueList':
            $response = product_attributes_getValueList($_GET['options_id'],$_GET['filter']);
            break;
        case 'product_variations_addToProduct':
            $response = product_variations_addToProduct($_GET['products_id'], $_GET['model'],$_GET['gtin'], $_GET['image_id'],$_GET['attributes'], $_GET['price']);
            break;
        case 'product_variations_updateSortOrder':
            $response = product_variations_updateSortOrder($_GET['jsonData']);
            break;
        case 'product_variations_removeVariation':
            $response = product_variations_removeVariation($_GET['products_variations_id']);
            break;
        case 'product_autodesk_link_search':
           $response = product_autodesk_link_search($_GET['search']);
            break;
		case 'product_autodesk_link_setLink':
			$response = product_autodesk_link_setLink($_GET['products_id'], $_GET['autodesk_catalog_id']);
			Break;
		case 'product_images_removeImage':
            $response = product_images_removeImage($_GET['products_id'],$_GET['image_id']);
        break;
        default:
            $response = "Invalid action.";
            break;
		}
    	
}else{
	$response = "no action detected";
}
header('Content-type: text/json');		
echo $response;


function dashboard_liveOrder($lastOrder) {
    $orders_query = tep_db_query("select o.orders_id, o.customers_name,o.customers_company, greatest(o.date_purchased, ifnull(o.last_modified, 0)) as date_last_modified, s.orders_status_name, ot.text as order_total from orders o, orders_total ot, orders_status s where greatest(o.date_purchased, ifnull(o.last_modified, 0)) > '" . $lastOrder . "' and o.orders_id = ot.orders_id and ot.class = 'ot_total' and o.orders_status = s.orders_status_id and s.language_id = 1 order by date_last_modified desc limit 6");
    $response = array();
    while ($orders = tep_db_fetch_array($orders_query)) {
        $response[] = array(
            "company" => tep_output_string_protected($orders['customers_company']),
            "title" => tep_output_string_protected($orders['customers_name']),
            "total" => strip_tags($orders['order_total']),
            "date" => $orders['date_last_modified'],
            "status" => $orders['orders_status_name'],
            "id" => $orders['orders_id']
        );
    }
    return json_encode(array("orders" => $response));
}

function related_products_addRelatedProduct($products_id, $relatedProductId, $relatedSortOrder, $relatedTableId) {
	$products_query_sql = "SELECT
												p.products_id,
												p.products_model,
												pd.products_name,
												r.products_related_id,
												r.products_related_table_id,
												r.products_related_related_products_id
											FROM
								products_related r
							JOIN products p ON p.products_id = r.products_related_related_products_id
							JOIN products_description pd ON pd.products_id = r.products_related_related_products_id
							JOIN manufacturers m ON m.manufacturers_id = p.manufacturers_id									
											WHERE
								p.products_id = '". $relatedProductId ."'
								AND	r.products_related_products_id = '". $products_id ."'
								AND r.products_related_related_products_id = ". $relatedProductId . ";";

	$products_query = tep_db_query($products_query_sql);
    $response = array();
    if (tep_db_num_rows($products_query) == 0) { 
		$add_query = tep_db_query("INSERT INTO `products_related` (
				`products_related_id`, 
				`products_related_products_id`, 
				`products_related_related_products_id`, 
				`products_related_related_category_id`, 
				`products_related_direction`, 
				`products_related_sort_order`, 
				`products_related_table_id`
			) VALUES (
				NULL, 
				'" . $products_id . "', 
				'" . $relatedProductId . "', 
				NULL, 
				'0',
				'" . $relatedSortOrder . "', 
				'" . $relatedTableId . "'
			)
											");
		$products_query = tep_db_query($products_query_sql);
	}	
	while ($products = tep_db_fetch_array($products_query)) {
		$patterns = array('/ +/', '/[<>()""]/');
		$replace = array(' ', '');
		$productName = preg_replace($patterns, $replace, trim(strip_tags($products['products_name'])));
		$variations = array();
		$variations_object = new tcs_product_attributes($products['products_id']);						
		if ($variations_object->has_variations){	
			$variations = $variations_object->get_variations();
		}
		$response[] = array(
			"title" => $productName,
			"id" => $products[$relatedProductId],
			"model" => $products['products_model'],
			"relatedId" => $products['products_related_id'],
			"relatedTableID" => $products['products_related_table_id'],
			"variations" =>  $variations
		);
	}
	
	return json_encode(array("Complete" => 2, "products" => $response, "query" => $products_query_sql));
    
}

function related_products_copyRelatedListFrom($sourceId, $products_id) {
    $get_related_products_query = tep_db_query("SELECT * FROM products as p, products_description as d, products_related as r WHERE r.products_related_related_products_id = d.products_id AND r.products_related_related_products_id=p.products_id AND r.products_related_products_id=" . $sourceId . " ORDER BY r.products_related_table_id, r.products_related_sort_order");
    $response = array();
    if (tep_db_num_rows($get_related_products_query) < 1) {
        return json_encode(array("Complete" => 0));
    } else {
        while ($products = tep_db_fetch_array($get_related_products_query)) {
            $exists_query = tep_db_query("SELECT * FROM products as p, products_related as r WHERE r.products_related_products_id=" . $products_id . " AND r.products_related_related_products_id=" . $products['products_related_related_products_id']);
            if (tep_db_num_rows($exists_query) < 1) {
                $add_query = tep_db_query("INSERT INTO 'products_related' ('products_related_id', 'products_related_products_id', 'products_related_related_products_id', 'products_related_related_category_id', 'products_related_direction', 'products_related_sort_order', 'products_related_table_id') VALUES (NULL, '" . $products_id . "', '" . $products['products_related_related_products_id'] . "', NULL, '0', '" . $products['products_related_sort_order'] . "','" . $products['products_related_table_id'] . "')");
                $lastId = tep_db_insert_id();
                $patterns = array('/ +/', '/[<>()""]/');
                $replace = array(' ', '');
                $productName = preg_replace($patterns, $replace, trim(strip_tags($products['products_name'])));
                $response[] = array(
                    "title" => $productName,
                    "id" => $products['products_id'],
                    "model" => $products['products_model'],
                    "sortOrder" => $products['products_related_sort_order'],
                    "price" => $products['products_price'],
                    "relatedId" => $lastId,
                    "relatedTableID" => $products['products_related_table_id'],
                    "added" => $add_query
                );
            }
        }
        return json_encode(array("Complete" => 2, "products" => $response));
    }
}

function related_products_updateSortOrder($jsonData) {
    $updatedData = json_decode($jsonData, true);
    if ($updatedData !== null) {
        foreach ($updatedData as $attributesId => $updatedValue) {
            $orders_sql = "UPDATE 'products_related' SET 'products_related_sort_order' = " . $updatedValue . " WHERE 'products_related'.'products_related_id' = " . $attributesId;
            $orders_query = tep_db_query($orders_sql);
        }
        return "Changes processed successfully.";
    } else {
        return "Invalid JSON data received.";
    }
}

function related_products_removeRelatedProduct($productsRelatedId) {
    $orders_query = tep_db_query("DELETE FROM products_related WHERE products_related.products_related_id = " . $productsRelatedId);
	$variationsquery = tep_db_query("DELETE FROM products_variations_to_products_related WHERE products_variations_to_products_related.products_related_id = " . $productsRelatedId);
    return json_encode(array("success" => 1));
}



function related_products_searchRelatedProduct($searchTerm) {
    // Split the search term into individual words
    $words = explode(' ', $searchTerm);
    
    $searchConditions = [];
    foreach ($words as $word) {
        $word = trim($word);
        if (!empty($word)) {
            // Prepare each word for the LIKE clause and safely escape it
            $likeClause = "'%" . tep_db_input($word) . "%'";
            $searchConditions[] = "(pd.products_name LIKE $likeClause OR p.products_model LIKE $likeClause OR m.manufacturers_name LIKE $likeClause)";
        }
    }

    // Combine all search conditions with AND
    $conditions = implode(' AND ', $searchConditions);
    
    // Construct the full SQL query using the conditions
    $products_query = tep_db_query("SELECT p.products_id, pd.products_name, cd.categories_name 
                                    FROM products p
                                    JOIN products_description pd ON pd.products_id = p.products_id
                                    JOIN manufacturers m ON m.manufacturers_id = p.manufacturers_id
                                    JOIN products_to_categories p2c ON p.products_id = p2c.products_id
                                    JOIN categories_description cd ON cd.categories_id = p2c.categories_id
                                    WHERE " . $conditions . " AND p.products_status = 1
                                    ORDER BY p.products_sort_order");

    $response = array();
    while ($products = tep_db_fetch_array($products_query)) {
        $productName = preg_replace(array('/ +/', '/[<>()""]/'), array(' ', ''), trim(strip_tags($products['products_name'])));
        $categories_name = preg_replace(array('/ +/', '/[<>()""]/'), array(' ', ''), trim(strip_tags($products['categories_name'])));
        $response[] = array(
            "title" => $productName . ' [' . $categories_name . ']',
            "id" => $products['products_id']
        );
    }
    return json_encode(array("products" => $response));
}


function product_attributes_addToProduct($products_id, $optionsId, $valuesId, $valuePrice, $pricePrefix, $attributeDefault, $dependsonOptionsId, $dependsonValuesId) {
	$attrib_query = tep_db_query("select * from products_attributes pa, products_description pd WHERE pa.products_id = pd.products_id and pa.products_id = '" . $products_id . "' and options_values_id = '" . $valuesId . "' and options_id = '" . $optionsId . "' order by pd.products_name");
	$defaultQuery = tep_db_query("select products_id, options_id, attribute_default from products_attributes where products_id = '" . $products_id . "' and options_id = '" . $optionsId . "' and attribute_default = 1");
	//echo "select products_id, options_id, attribute_default from products_attributes where products_id = '" . $products_id . "' and options_id = '" . $optionsId . "' and attribute_default = 1";
	
	
	if (tep_db_num_rows($defaultQuery) > 0) {
		$attributeDefault = "0";
	}
	if (tep_db_num_rows($attrib_query) > 0) {
		$update_query = tep_db_query("UPDATE products_attributes SET `options_id` = '" . $optionsId . "', `options_values_id` = '" . $valuesId . "', `options_values_price` = '" . $valuePrice . "', `price_prefix` = '" . $pricePrefix . "', `attribute_default` = '" . $attributeDefault . "', `dependson_options_id` = '" . $dependsonOptionsId . "', `dependson_options_values_id` = '" . $dependsonValuesId . "' WHERE `products_id` = '" . $products_id . "' AND options_values_id = '" . $valuesId . "' AND options_id = '" . $optionsId . "'");
		$response = array();
	} else {
		$add_query = tep_db_query("INSERT INTO products_attributes (`products_attributes_id`, `products_id`, `options_id`, `options_values_id`, `options_values_price`, `price_prefix`,`attribute_default`,`dependson_options_id`,`dependson_options_values_id`) VALUES (NULL, '" . $products_id . "', '" . $optionsId . "', '" . $valuesId . "','" . $valuePrice . "' ,'" . $pricePrefix  . "', '" . $attributeDefault . "','" . $dependsonOptionsId . "','" . $dependsonValuesId . "')");
		$response = array();
	}
	$products_query = tep_db_query("select * from products_attributes pa,  products_description pd WHERE pa.products_id = '" . $products_id . "' and pa.products_id = pd.products_id and options_id = '" . $optionsId . "' and options_values_id = '" . $valuesId . "'");
	$attributes_query = tep_db_query("select * from  products_options po, products_options_values pov WHERE po.products_options_id = '" . $optionsId . "' and pov.products_options_values_id = '" . $valuesId . "'");
	while ($attributes = tep_db_fetch_array($products_query)) {
		$options_name = tep_options_name($attributes['options_id']);
		$values_name = tep_values_name($attributes['options_values_id']);
		$dependson_options_values_ids = explode(',',$attributes['dependson_options_values_id']);
		$dependson_values_name = "";
		foreach ($dependson_options_values_ids as $value){
			if ($dependson_values_name != ""){
				$dependson_values_name .=  ', ';
			}
			$dependson_values_name .= tep_values_name($value);
		}
		$dependson_options_name = tep_options_name($attributes['dependson_options_id']);
		//$dependson_values_name = tep_values_name($attributes['dependson_options_values_id']);
		$productName = preg_replace(array('/ +/', '/[<>()""]/'), array(' ', ''), trim(strip_tags($attributes['products_name'])));
		$response[] = array(
		    	"title" => $productName,
		    	"attribute_id" => $attributes['products_attributes_id'],
		    	"products_id"=> $attributes['products_id'],
			"options_id" => $attributes['options_id'],
			"values_id" => $attributes['options_values_id'],
			"options_name" => $options_name,
			"values_name" => htmlentities($values_name),
			"value_price" => $attributes['options_values_price'],
			"price_prefix" => $attributes['price_prefix'],
			"dependson_options_name" => $dependson_options_name,
			"dependson_values_name" => $dependson_values_name,
			"dependson_options_id" => $attributes['dependson_options_id'],
			"dependson_values_id" => $attributes['dependson_options_values_id'],
			"attribute_default" => $attributes['attribute_default']
		);
	}

	// Generate dependency warnings after the insert/update
	//$products_attributes = new tcs_product_attributes($products_id);
	//$dependency_warnings = tcs_draw_attributes_dependency_warnings($products_attributes);
	// Note: This legacy function returns JSON, so we can't include HTML warnings here
	// The warnings will be updated when the page refreshes or through other means

	return json_encode(array("Complete" => (tep_db_num_rows($attrib_query) > 0) ? 1 : 2, "attributes" => $response));
}

function product_attributes_updateSortOrder($jsonData) {
	$updatedData = json_decode(stripslashes($jsonData), true);

	if ($updatedData !== null) {
		$products_id = null;
		foreach ($updatedData as $attributesId => $updatedValue) {
			// Get product ID for dependency warnings (only need to do this once)
			if ($products_id === null) {
				$product_query = tep_db_query("SELECT products_id FROM products_attributes WHERE products_attributes_id = " . (int)$attributesId);
				$product_data = tep_db_fetch_array($product_query);
				$products_id = $product_data['products_id'];
			}

			$orders_sql = "UPDATE products_attributes SET products_attributes_sort_order = " . $updatedValue . " WHERE products_attributes.products_attributes_id = " . $attributesId;
            print_rr($orders_sql);
			$orders_query = tep_db_query($orders_sql);
		}

		// Generate dependency warnings after all updates
		if ($products_id) {
			//$products_attributes = new tcs_product_attributes($products_id);
			//$dependency_warnings = tcs_draw_attributes_dependency_warnings($products_attributes);
			// Note: This legacy function returns JSON, so we can't include HTML warnings here
			// The warnings will be updated when the page refreshes or through other means
		}

		return "Changes processed successfully.";
	} else {
		return "Invalid JSON data received.";
	}
}

function product_attributes_removeAttribute($productsAttributesId) {
	// Get the product ID first to generate dependency warnings
	$product_query = tep_db_query("SELECT products_id FROM products_attributes WHERE products_attributes_id = " . (int)$productsAttributesId);
	$product_data = tep_db_fetch_array($product_query);
	$products_id = $product_data['products_id'];

	tep_db_query("DELETE FROM products_attributes WHERE products_attributes_id = " . $productsAttributesId);
	$attrib_query = tep_db_query("select * FROM products_attributes WHERE products_attributes_id = " . $productsAttributesId);

	// Generate dependency warnings after the deletion
	//if ($products_id) {
//		$products_attributes = new tcs_product_attributes($products_id);
//		$dependency_warnings = tcs_draw_attributes_dependency_warnings($products_attributes);
		// Note: This legacy function returns JSON, so we can't include HTML warnings here
		// The warnings will be updated when the page refreshes or through other means
	//}

	Return json_encode(array("Complete" => (tep_db_num_rows($attrib_query) == 0) ? 1 : 2));
}

function product_attributes_getValueList($optionsId, $filter = null) {
	if (isset($filter)){ 
		$attrib_query = tep_db_query("select * from products_options_values_to_products_options pov, products_options_values po, products_attributes pa WHERE pov.products_options_id = '" . $optionsId . "' and po.products_options_values_id = pov.products_options_values_id and po.products_options_values_id = pa.options_values_id and pa.products_id='" . $filter . "' order by pa.products_attributes_sort_order");
	}else{
		$attrib_query = tep_db_query("select * from products_options_values_to_products_options pov, products_options_values po WHERE pov.products_options_id = '" . $optionsId . "' and po.products_options_values_id = pov.products_options_values_id order by products_options_values_name");
	}
	if (tep_db_num_rows($attrib_query) == 0) {
		return json_encode(array("Complete" => 1));
	} else {
		$response = array();
		while ($attributes = tep_db_fetch_array($attrib_query)) {
			$response[] = array(
			"options_id" => $attributes['products_options_id'],
			"values_id" => $attributes['products_options_values_id'],
			"values_name" => htmlentities($attributes['products_options_values_name'])
			);
		}
	return json_encode(array("Complete" => 2, "attributes" => $response));
	}
}

function product_variations_addToProduct($products_id, $model, $gtin ,$image_id, $attributes, $price) {
	$variations_query = tep_db_query("select * from products_variations pv WHERE pv.products_id = '" . $products_id . "' and pv.attributes = '" . $attributes . "' and pv.model = '" . $model . "'");
	if (tep_db_num_rows($variations_query) > 0) {
		$update_query = tep_db_query("UPDATE `products_variations` SET `model` = '" . $model . "', `gtin` = '" . $gtin . "', `image_id` = '" . $image_id ."', `attributes` = '" . $attributes . "', `price` = '" . $price . "' WHERE `products_id` = '" . $products_id . "' AND model = '" . $model . "'");
		$response = array();
	} else {
		$add_query = tep_db_query("INSERT INTO `products_variations` (`products_variations_id`, `products_id`, `model`, `gtin`,`image_id`, `attributes`, `price`) VALUES (NULL, '" . $products_id . "', '" . $model . "', '" . $gtin . "', '" . $image_id. "',  '" . $attributes . "','" . $price . "')");
		$response = array();
	}
	$variations_query = tep_db_query("select * from products_variations pv WHERE pv.products_id = '" . $products_id . "' and pv.attributes = '" . $attributes . "' and pv.model = '" . $model . "'");

	//print_rr("select * from products_variations pv WHERE pv.products_id = '" . $products_id . "' and pv.attributes = '" . $attributes . "' and pv.model = '" . $model . "'","poop23");
		//$attributes_query = tep_db_query("select * from  products_options po, products_options_values pov WHERE po.products_model = '" . $model . "' and pov.products_options_attributes = '" . $attributes . "'");
	while ($variations = tep_db_fetch_array($variations_query)) {	
	    
	    	$attributes = explode('{', substr($variations['attributes'], strpos($variations['attributes'], '{')+1));
	  	$attributes_text = "";
	  	for ($i=0, $n=sizeof($attributes); $i<$n; $i++) {
	            $pair = explode('}', $attributes[$i]);
		    $attributes_text .= '<span style="font-weight:bold">' . tep_options_name($pair[0]) . ':</span> ';
		    $attributes_text .= tep_values_name($pair[1]) . ' ';	
	        }
		$response[] = array(
		    	"products_id" => $products_id,
		    	"products_variations_id" => $variations['products_variations_id'],
		    	"attributes_text" => $attributes_text,
		    	"attributes" => $variations['attributes'],
			"model" => $variations['model'],
			"gtin" => $variations['gtin'],
			"image_id" => $variations['image_id'],
			"price" => $variations['price'],
		);
	}
	return json_encode(array("Complete" => (tep_db_num_rows($variations_query) > 0) ? 1 : 2, "variations" => $response));
}


function product_variations_removeVariation($products_variations_id) {	
	tep_db_query("DELETE FROM products_variations WHERE products_variations_id = " . $products_variations_id);
    $variations_query = tep_db_query("select * FROM products_variations WHERE products_variations_id = " . $products_variations_id);
	Return json_encode(array("Complete" => (tep_db_num_rows($variations_query) == 0) ? 1 : 2));
}

function product_variations_updateSortOrder($jsonData) {
	$updatedData = json_decode(stripslashes($jsonData), true);
 	if ($updatedData !== null) {
         print_rr($updatedData);
		foreach ($updatedData as $variationsId => $updatedValue) {
			$orders_sql = "UPDATE products_variations SET sort_order = " . $updatedValue . " WHERE products_variations_id = " . $variationsId;
            print_rr($orders_sql);
			$orders_query = tep_db_query($orders_sql);
		}
		return json_encode($updatedData);
	} else {
		return "Invalid JSON data received.";
	}
}

function product_images_removeImage($products_id,$id){
	   tep_db_query("delete from products_images where products_id = '" . (int)$products_id . "' AND id='" . (int)$id . "'");
	  //echo "delete from products_images where products_id = '" . (int)$products_id . "' AND id='" . (int)$id . "'";
	   $response[] = array("succces" => 2,);
	 	return json_encode(array("Complete" => 2, "attributes" => $response));
}
function related_products_variations_toggle($products_id,$related_id,$variation_id,$status){
	$related_query = tep_db_query
("
		select * 
		from products_variations_to_products_related 
		WHERE
			products_id = '" . $products_id . "' 
				and products_related_id = '" . $related_id . "'
				and products_variations_id = '" . $variation_id . "'"
	);
	if (tep_db_num_rows($related_query) > 0) {
		$query = "
			UPDATE products_variations_to_products_related 
			SET 
				status = '" . $status . "' 
			WHERE 
				products_id='" . $products_id . "' 
					AND products_related_id = '" . $related_id . "' 
					AND products_variations_id = '" . $variation_id . "'";	
	} else {
		$query = "
			INSERT INTO 
				`products_variations_to_products_related` 
				(
					`products_id`, 
					`products_related_id`,
					`products_variations_id`,
					`status`
				) VALUES (
					'" . $products_id . "',
					'" . $related_id . "',
					'" . $variation_id . "',
					'" . $status . "'
			)";			
	}
	//print_rr($query);
	$update_query = tep_db_query($query);
	$response[] = array("succces" => 2,$query);
	 	return json_encode(array("Complete" => 2, "response" => $update_query));
}

//function product_autodesk_link_search() {
    //$query = "SELECT * FROM products_autodesk_catalog";
	//$pdo = tep_db_connect();
    //try {
        //$stmt = $pdo->query($query);
        //$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        //echo json_encode($results);
    //} catch (PDOException $e) {
        //echo json_encode(['error' => "Could not fetch data: " . $e->getMessage()]);
    //}
	//tep_db_close();
//}
function product_autodesk_link_search($search_term) {
	if (!empty($search_term)) {
		// search products
		$query = "SELECT * FROM products_autodesk_catalog WHERE ";
		$words = explode(" ", $search_term);
		
		// Get table columns
		
		$columns = ['offeringName', 'intendedUsage_description', 'accessModel_description', 'servicePlan_description', 'connectivity_description', 'term_description'];
				
		// Build the query
		$conditions = [];
		foreach ($words as $word) {
			$word = trim($word);
			if (!empty($word)) {
				$word_conditions = [];
				foreach ($columns as $column) {
					$word_conditions[] = $column . " LIKE '%" . tep_db_input($word) . "%'";
				}
				if (!empty($word_conditions)) {
					$conditions[] = "(" . implode(" OR ", $word_conditions) . ")";
				}
			}
		}
		
		if (!empty($conditions)) {
			$query .= implode(" AND ", $conditions);
		} else {
			// No valid search terms, handle this case as needed
			//$query = "SELECT * FROM products_autodesk_catalog"; // or some other default behavior
		}
		$query .= " AND NOT (`intendedUsage_code` = 'NFR')";
		$query .= " ORDER BY orderAction, offeringName, accessModel_description, term_description, specialProgramDiscount_code";
		// Execute the query		
		try {
			$results = tep_db_fetch_all(tep_db_query($query));			
			//$results[] = array("columns" => $columns);			
			$results[] = array("query" => $query);
			return json_encode($results);
		} catch (PDOException $e) {		
			return  json_encode(['error' => "Could not search data: " . $e->getMessage()]);
		}
	} else{
		return json_encode(['error' => "Could not search data: No search term provided"]);
	}
}


function product_autodesk_link_setLink($products_id,$products_autodesk_catalog_id){ 
	//$query = "select * FROM products_to_autodesk_catalog p2a JOIN products_autodesk_catalog pac on p2a.products_autodesk_catalog_id = pac.id  WHERE " . "products_id = '" . $products_id . "' AND products_autodesk_catalog_id = '" . $products_autodesk_catalog_id . "'";
	$query_sql = "select * from products_to_autodesk_catalog p2a JOIN products_autodesk_catalog pac on p2a.unique_hash = pac.unique_hash WHERE p2a.products_id = '" . (int) $products_id . "' Limit 1";
	//get product hash
	$query_hash_sql = "select * from products_autodesk_catalog WHERE id = '" . $products_autodesk_catalog_id . "' LIMIT 1";
	$hash_query = tep_db_fetch_array(tep_db_query($query_hash_sql));	
	$hash = $hash_query['unique_hash'];
	if (tep_db_num_rows(tep_db_query($query_sql)) > 0) {
		$type = "update";	
		$query_set_sql = "UPDATE products_to_autodesk_catalog SET " . "products_autodesk_catalog_id = '" . $products_autodesk_catalog_id . "', unique_hash = '" . $hash . "' WHERE " . "products_id = '" . $products_id . "'";
	} else {	
		$type = "insert";			
		$query_set_sql  = "INSERT INTO products_to_autodesk_catalog (products_id, products_autodesk_catalog_id, unique_hash) VALUES ('" . $products_id . "', '" . $products_autodesk_catalog_id . "', '" . $hash . "')";
	}
	tep_db_query($query_sql);
	$results = tep_db_fetch_array(tep_db_query($query_sql));
	return json_encode(array("Complete" => 1, "type" => $type, "product" => $results, "query" => $query_set_sql ));
}
?>