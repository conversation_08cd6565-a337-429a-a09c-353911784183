<?php
/**
 * Example usage of the new body_only option for tcs_draw_attributes_table
 * and tcs_draw_admin_bootstrap_table functions
 */

// Example 1: Get full table (default behavior)
$products_attributes = new tcs_product_attributes($products_id);
$full_table = tcs_draw_attributes_table($products_attributes, $products_id);
echo $full_table; // Returns complete table with container, header, body, and footer

// Example 2: Get only table body for HTMX replacement
$products_attributes = new tcs_product_attributes($products_id);
$table_body_only = tcs_draw_attributes_table($products_attributes, $products_id, true);
echo $table_body_only; // Returns only the <tbody>...</tbody> content

// Example 3: Using in product_attributes_updateSortOrder function
function example_updateSortOrder($jsonData, $products_id) {
    // Update sort orders in database...
    
    // Generate updated table body for HTMX replacement
    $products_attributes = new tcs_product_attributes($products_id);
    $updated_table_body = tcs_draw_admin_bootstrap_table(
        'Attributes',
        'attributesTable',
        'table table-striped table-draggable',
        [], // columns not needed for body only
        tcs_draw_attributes_table($products_attributes, $products_id, true), // body_only = true
        '', // footer not needed for body only
        ['hx-post' => 'api_h.php?action=product_attributes_updateSortOrder', 'hx-include' => '.attributesSOinput', 'hx-trigger' => 'end'],
        true // body_only = true
    );
    
    return $updated_table_body; // Returns <tbody id="attributesTable_body">...</tbody>
}

// Example 4: HTMX usage in frontend
?>
<script>
// When sort order is updated via drag and drop, HTMX will replace the table body
// The hx-target should be set to "#attributesTable_body" and hx-swap to "outerHTML"
// This allows seamless replacement of just the table body without affecting the header or footer
</script>

<!-- Example HTML structure -->
<div class="panel panel-default">
    <div class="panel-header">
        <div class="panel-body">Attributes</div>
    </div>
    <table id="attributesTable" class="table table-striped table-draggable" 
           hx-post="api_h.php?action=product_attributes_updateSortOrder" 
           hx-include=".attributesSOinput" 
           hx-trigger="end">
        <thead>
            <tr>
                <td class="">&nbsp;</td>
                <td class="">Option</td>
                <td class="">Value</td>
                <td class="text-center">Default</td>
                <td class="text-center">DependsOn</td>
                <td class="text-center">Sort Order</td>
                <td class="text-center" colspan="2">Actions</td>
            </tr>
        </thead>
        <!-- This tbody will be replaced by HTMX when sort order is updated -->
        <tbody id="attributesTable_body">
            <!-- Table rows go here -->
        </tbody>
    </table>
    <div class="panel-footer">
        <!-- Footer content -->
    </div>
</div>
